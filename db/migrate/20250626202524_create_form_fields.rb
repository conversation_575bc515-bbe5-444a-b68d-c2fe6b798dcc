# db/migrate/YYYYMMDDHHMMSS_create_form_fields.rb

class CreateFormFields < ActiveRecord::Migration[8.0]
  def change
    create_table :form_fields do |t|
      t.references :form, null: false, foreign_key: true
      t.string :field_type, null: false
      t.boolean :required, default: false, null: false
      t.jsonb :options, null: false, default: {}
      t.integer :position, null: false, default: 0
      t.jsonb :translations, null: false, default: {}
      t.timestamps
    end

    add_index :form_fields, [:form_id, :position]
    add_index :form_fields, :translations, using: :gin
  end
end