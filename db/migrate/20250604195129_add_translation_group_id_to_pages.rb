class AddTranslationGroupIdToPages < ActiveRecord::Migration[8.0]
  def up
    # Přid<PERSON>me sloupec jako nullable (pro nové stránky b<PERSON><PERSON> vytváření)
    add_column :pages, :translation_group_id, :bigint

    # Nastavíme hodnoty pro existují<PERSON><PERSON> stránky
    Page.find_each do |page|
      page.update_column(:translation_group_id, page.id)
    end

    # Přidáme foreign key a index (ale necháme nullable pro nové stránky)
    add_foreign_key :pages, :pages, column: :translation_group_id
    add_index :pages, :translation_group_id
  end

  def down
    remove_foreign_key :pages, column: :translation_group_id
    remove_index :pages, :translation_group_id
    remove_column :pages, :translation_group_id
  end
end
