class AddMediaTypeToMediaCollections < ActiveRecord::Migration[8.0]
  def change
    # 1. Přidá sloupec media_type_id jako nulovatelný
    add_reference :media_collections, :media_type, null: true, foreign_key: true

    # 2. Aktualizuje všechny existující záznamy a nastaví media_type_id na 2
    # Ujistěte se, že MediaType s ID 2 existuje.
    # Použijeme `in_batches` pro případ velkého množství z<PERSON>namů,
    # i když pro `update_all` to nen<PERSON> striktně nutné, je to dobr<PERSON> praxe.
    MediaCollection.in_batches.update_all(media_type_id: 2) if defined?(MediaCollection) && MediaCollection.exists?

    # 3. Změní sloupec media_type_id na NOT NULL
    # Toto se provede až poté, co jsou všechny existující záznamy aktualizovány.
    change_column_null :media_collections, :media_type_id, false
  end
end