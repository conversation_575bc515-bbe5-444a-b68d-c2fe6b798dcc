class RefactorBlockControlsToUseMobilityContainer < ActiveRecord::Migration[8.0]
  # Definu<PERSON><PERSON>, kter<PERSON> klíče z `options` jsou přeložitelné
  TRANSLATABLE_OPTIONS_KEYS = %w[pre_header heading_type primary_button_text secondary_button_text]

  def up
    # 1. Přidáme nový sloupec pro kontejner
    add_column :block_controls, :translations, :jsonb, default: {}, null: false

    # 2. <PERSON><PERSON><PERSON><PERSON> skriptu provedeme složitou konsolidaci dat
    #    SQL by bylo příli<PERSON> složité kvůli STI a logice v `options`.
    ActiveRecord::Base.transaction do
      # Seskupíme všechny verze jedné komponenty. Klíčem je `block_id` a `position`.
      # Zpracováváme jen ty, které mají více jazykových verzí.
      grouped_controls = BlockControl.unscoped.group_by { |c| [c.block_id, c.position] }

      grouped_controls.each do |(block_id, position), controls|
        next if controls.empty?

        # Vybereme si jeden z<PERSON>znam jako "hlavní" (např. ten český, nebo prostě první)
        # Na tento záznam budeme slučovat data z ostatních.
        base_control = controls.find { |c| c.locale == 'cs' } || controls.first
        other_controls = controls - [base_control]

        new_translations = {}

        # Zpracujeme všechny jazykové verze
        ([base_control] + other_controls).each do |control|
          locale_key = control.locale.to_sym
          new_translations[locale_key] = {}

          # Přesuneme hodnotu ze sloupce `text`
          new_translations[locale_key][:text] = control.text

          # Z `options` vybereme jen přeložitelné klíče
          control.options&.each do |key, value|
            if TRANSLATABLE_OPTIONS_KEYS.include?(key.to_s)
              new_translations[locale_key][key.to_sym] = value
            end
          end
        end

        # Uložíme sloučené překlady do hlavního záznamu
        base_control.update_column(:translations, new_translations)

        # Smažeme ostatní (již nepotřebné) jazykové verze
        other_controls.each(&:destroy)
      end
    end

    # 3. Odstraníme staré, již nepotřebné sloupce
    remove_column :block_controls, :locale, :string
    remove_column :block_controls, :text, :text
  end

  # Metoda `down` by byla extrémně složitá (rozdělování kontejneru zpět do řádků).
  # Pro jednorázovou refaktorizaci je bezpečnější označit migraci jako nevratnou.
  def down
    raise ActiveRecord::IrreversibleMigration
  end
end
