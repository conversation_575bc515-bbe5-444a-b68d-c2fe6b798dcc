class ConsolidateTranslationsForPricingItems < ActiveRecord::Migration[8.0]
  # <PERSON><PERSON><PERSON> sloupců, k<PERSON><PERSON> budeme slučovat do kontejneru
  COLUMNS_TO_CONSOLIDATE = %i[name description slug content]

  def up
    say_with_time "Consolidating translations into a single container column" do
      # 1. Přidáme nový sloupec 'translations', kam vše sloučíme.
      #    Je důležité nastavit NOT NULL a výchozí hodnotu prázdný JSONB objekt.
      add_column :pricing_items, :translations, :jsonb, default: {}, null: false

      # 2. Pomocí SQL a JSONB funkcí přesuneme data.
      #    Tento SQL příkaz je komplexní, ale extrémně výkonný.
      #    Pro každý řádek vezme všechny jazyky (klíče) z 'name' sloupce
      #    a pro každý jazyk vytvoří nový objekt s klíči 'name', 'description' atd.
      #    a jejich př<PERSON>šnými hodnotami.
      execute <<-SQL
        UPDATE pricing_items
        SET translations = (
          SELECT jsonb_object_agg(key, jsonb_build_object(
            'name',        name -> key,
            'description', description -> key,
            'slug',        slug -> key,
            'content',     content -> key
          ))
          FROM jsonb_object_keys(name) AS key
        )
        WHERE jsonb_typeof(name) = 'object';
      SQL

      # 3. Po úspěšné migraci dat odstraníme staré, již nepotřebné sloupce.
      remove_columns :pricing_items, *COLUMNS_TO_CONSOLIDATE, type: :jsonb
    end
  end

  def down
    say_with_time "De-consolidating translations back to individual columns" do
      # Kroky pro bezpečný návrat zpět (pokud by bylo potřeba)

      # 1. Znovu vytvoříme oddělené sloupce
      COLUMNS_TO_CONSOLIDATE.each do |column_name|
        add_column :pricing_items, column_name, :jsonb, default: {}, null: false
      end

      # 2. Pro každý sloupec zvlášť extrahujeme data z kontejneru a naplníme ho.
      COLUMNS_TO_CONSOLIDATE.each do |column_name|
        execute <<-SQL
          UPDATE pricing_items
          SET #{column_name} = (
            SELECT jsonb_object_agg(key, value -> '#{column_name}')
            FROM jsonb_each(translations) AS t(key, value)
            WHERE value ? '#{column_name}'
          );
        SQL
      end

      # 3. Odstraníme kontejnerový sloupec
      remove_column :pricing_items, :translations, :jsonb
    end
  end
end