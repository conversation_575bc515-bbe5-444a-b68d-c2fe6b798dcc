class ChangeNameToHstoreInPricingSections < ActiveRecord::Migration[8.0]
  def up
    # Dočasn<PERSON> přidáme nový hstore sloupec
    add_column :pricing_sections, :name_hstore, :hstore, default: {}, null: false

    # Zkopírujeme stávající data do nového sloupce.
    # <PERSON><PERSON><PERSON><PERSON><PERSON>ládáme, že stávající názvy jsou v češtině ('cs').
    execute "UPDATE pricing_sections SET name_hstore = hstore('cs', name)"

    # Odstraníme starý stringový sloupec
    remove_column :pricing_sections, :name

    # Přejmenujeme nový sloupec na původní název 'name'
    rename_column :pricing_sections, :name_hstore, :name
  end

  def down
    # Kroky pro návrat zpět (pokud by by<PERSON> potřeb<PERSON>)
    add_column :pricing_sections, :name_string, :string
    execute "UPDATE pricing_sections SET name_string = name -> 'cs'"
    remove_column :pricing_sections, :name
    rename_column :pricing_sections, :name_string, :name
  end
end