class CreateBlockContainers < ActiveRecord::Migration[8.0]
  def change
    create_table :block_containers do |t|
      t.string :type
      t.references :block, null: false, foreign_key: true
      t.string :theme, null: true
      t.string :container, null: true
      t.string :padding_x, null: true
      t.string :padding_y, null: true
      t.string :media_alignment, null: true
      t.string :alignment, null: true
      t.string :background_overlay_opacity, null: true

      t.timestamps
    end
  end
end
