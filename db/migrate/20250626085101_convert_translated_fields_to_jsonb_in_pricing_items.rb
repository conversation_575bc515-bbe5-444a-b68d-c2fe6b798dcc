class ConvertTranslatedFieldsToJsonbInPricingItems < ActiveRecord::Migration[8.0]
  def up
    say_with_time "Converting hstore columns to jsonb for PricingItem" do
      # Pro každý sloupec provedeme operaci ve třech krocích:
      # 1. Odstraníme starý default
      # 2. Změníme typ sloupce (a převedeme data)
      # 3. Nastavíme nový, jsonb-kompatibilní default

      # --- Sloupec `name` ---
      change_column_default :pricing_items, :name, from: {}, to: nil
      change_column :pricing_items, :name, :jsonb, using: 'hstore_to_jsonb(name)', null: false
      change_column_default :pricing_items, :name, from: nil, to: {}

      # --- Sloupec `description` ---
      change_column_default :pricing_items, :description, from: {}, to: nil
      change_column :pricing_items, :description, :jsonb, using: 'hstore_to_jsonb(description)', null: false
      change_column_default :pricing_items, :description, from: nil, to: {}

      # --- Sloupec `slug` ---
      # U slugu p<PERSON><PERSON>, že mů<PERSON> b<PERSON>t null, takže null: true
      change_column_default :pricing_items, :slug, from: {}, to: nil
      change_column :pricing_items, :slug, :jsonb, using: 'hstore_to_jsonb(slug)', null: true
      change_column_default :pricing_items, :slug, from: nil, to: {}
    end
  end

  def down
    say_with_time "Converting jsonb columns back to hstore for PricingItem" do
      # Obrácený postup pro návrat zpět

      # --- Sloupec `name` ---
      change_column_default :pricing_items, :name, from: {}, to: nil
      change_column :pricing_items, :name, :hstore, using: 'hstore(ARRAY(SELECT ARRAY[key, value] FROM jsonb_each_text(name)))', null: false
      change_column_default :pricing_items, :name, from: nil, to: {}

      # --- Sloupec `description` ---
      change_column_default :pricing_items, :description, from: {}, to: nil
      change_column :pricing_items, :description, :hstore, using: 'hstore(ARRAY(SELECT ARRAY[key, value] FROM jsonb_each_text(description)))', null: false
      change_column_default :pricing_items, :description, from: nil, to: {}

      # --- Sloupec `slug` ---
      change_column_default :pricing_items, :slug, from: {}, to: nil
      change_column :pricing_items, :slug, :hstore, using: 'hstore(ARRAY(SELECT ARRAY[key, value] FROM jsonb_each_text(slug)))', null: true
      change_column_default :pricing_items, :slug, from: nil, to: {}
    end
  end
end