class MigrateBlocksToManyToManyRelationship < ActiveRecord::Migration[7.0]
  def up
    # Nejdříve zkopírujeme všechny existující bloky pro page_id 25 a 33
    duplicate_blocks_for_pages([25, 33])
    
    # Vytvoříme page_blocks záznamy pro všechny existují<PERSON><PERSON> bloky
    create_page_blocks_from_existing_blocks
    
    # Odstraníme page_id sloupec z blocks tabulky
    remove_column :blocks, :page_id
  end

  def down
    # Přidáme zpět page_id sloupec
    add_reference :blocks, :page, null: true, foreign_key: true
    
    # Obnovíme page_id z page_blocks (vezme první page pro každý block)
    restore_page_id_from_page_blocks
    
    # Smažeme page_blocks záznamy
    PageBlock.delete_all
    
    # Smažeme duplikované bloky (ty co mají stejný obsah ale jiné ID)
    remove_duplicated_blocks
  end

  private

  def duplicate_blocks_for_pages(page_ids)
    say "Duplikuji bloky pro stránky: #{page_ids.join(', ')}"
    
    # Najdeme všechny bloky, které nejsou už přiřazené k těmto stránk<PERSON>m
    existing_blocks = Block.where.not(page_id: page_ids)
    
    existing_blocks.find_each do |original_block|
      page_ids.each do |page_id|
        say "  Duplikuji blok #{original_block.id} pro stránku #{page_id}"
        
        # Vytvoříme kopii bloku
        new_block = original_block.dup
        new_block.page_id = page_id
        new_block.save!
        
        # Zkopírujeme všechny controls
        original_block.controls.find_each do |control|
          new_control = control.dup
          new_control.block = new_block
          new_control.save!
        end
        
        # Zkopírujeme všechny media
        original_block.media.find_each do |media|
          new_media = media.dup
          new_media.block = new_block
          new_media.save!
          
          # Zkopírujeme attached files pokud existují
          if media.image.attached?
            new_media.image.attach(media.image.blob)
          end
        end
        
        # Zkopírujeme background images pokud existují
        if original_block.background_image.attached?
          new_block.background_image.attach(original_block.background_image.blob)
        end
        
        if original_block.background_image_mobile.attached?
          new_block.background_image_mobile.attach(original_block.background_image_mobile.blob)
        end
      end
    end
  end

  def create_page_blocks_from_existing_blocks
    say "Vytvářím page_blocks záznamy z existujících blocks"
    
    Block.find_each do |block|
      next unless block.page_id.present?
      
      PageBlock.create!(
        block: block,
        page_id: block.page_id,
        position: block.position || 0
      )
    end
  end

  def restore_page_id_from_page_blocks
    say "Obnovuji page_id z page_blocks"
    
    # Pro každý block nastavíme page_id z prvního page_blocks záznamu
    Block.joins(:page_blocks).group('blocks.id').find_each do |block|
      first_page_block = block.page_blocks.order(:created_at).first
      block.update_column(:page_id, first_page_block.page_id)
    end
  end

  def remove_duplicated_blocks
    say "Odstraňuji duplikované bloky"
    
    # Toto je složitější - museli bychom identifikovat duplikáty podle obsahu
    # Pro jednoduchost jen upozorníme, že rollback nemusí být úplný
    say "VAROVÁNÍ: Rollback neodstraní duplikované bloky. Proveďte to manuálně pokud je potřeba."
  end
end
