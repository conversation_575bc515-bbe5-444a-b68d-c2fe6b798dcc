require "test_helper"

class Admin::Content::LayoutsControllerTest < ActionDispatch::IntegrationTest
  test "should get index" do
    get admin_content_layouts_index_url
    assert_response :success
  end

  test "should get new" do
    get admin_content_layouts_new_url
    assert_response :success
  end

  test "should get create" do
    get admin_content_layouts_create_url
    assert_response :success
  end

  test "should get edit" do
    get admin_content_layouts_edit_url
    assert_response :success
  end

  test "should get update" do
    get admin_content_layouts_update_url
    assert_response :success
  end

  test "should get destroy" do
    get admin_content_layouts_destroy_url
    assert_response :success
  end
end
