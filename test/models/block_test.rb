# == Schema Information
#
# Table name: blocks
#
#  id                  :bigint           not null, primary key
#  hidden_at           :datetime
#  media_options       :jsonb
#  name                :string
#  options             :jsonb
#  position            :integer
#  pricing_options     :jsonb
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  media_collection_id :bigint
#  media_type_id       :bigint
#  pricing_id          :bigint
#
# Indexes
#
#  index_blocks_on_media_collection_id  (media_collection_id)
#  index_blocks_on_media_type_id        (media_type_id)
#  index_blocks_on_pricing_id           (pricing_id)
#
# Foreign Keys
#
#  fk_rails_...  (media_collection_id => media_collections.id)
#  fk_rails_...  (media_collection_id => media_collections.id)
#  fk_rails_...  (media_type_id => media_types.id)
#  fk_rails_...  (pricing_id => pricing.id)
#
require "test_helper"

class BlockTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
