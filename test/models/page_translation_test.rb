require "test_helper"

class PageTranslationTest < ActiveSupport::TestCase
  def setup
    @account = Account.create!(name: "Test Account")
    @website = Website.create!(
      account: @account,
      name: "Test Website",
      email: "<EMAIL>",
      phone: "+************",
      locale: "cs",
      available_locales: ["cs", "en"]
    )
    
    ActsAsTenant.current_tenant = @website
  end

  test "new page gets its own translation_group_id" do
    page = Page.create!(title: "Test Page", locale: "cs", type: "Content")
    
    assert_equal page.id, page.translation_group_id
    assert page.is_translation_master?
  end

  test "translation page references master page" do
    master_page = Page.create!(title: "Kontakt", locale: "cs", type: "Content")
    translation_page = Page.create!(
      title: "Contact", 
      locale: "en", 
      type: "Content",
      translation_group_id: master_page.id
    )
    
    assert_equal master_page.id, translation_page.translation_group_id
    assert master_page.is_translation_master?
    assert_not translation_page.is_translation_master?
  end

  test "finding translations works correctly" do
    master_page = Page.create!(title: "Kontakt", locale: "cs", type: "Content")
    en_page = Page.create!(
      title: "Contact", 
      locale: "en", 
      type: "Content",
      translation_group_id: master_page.id
    )
    de_page = Page.create!(
      title: "Kontakt", 
      locale: "de", 
      type: "Content",
      translation_group_id: master_page.id
    )
    
    # Test all_translations
    assert_equal 3, master_page.all_translations.count
    assert_includes master_page.all_translations, en_page
    assert_includes master_page.all_translations, de_page
    
    # Test other_translations
    assert_equal 2, master_page.other_translations.count
    assert_not_includes master_page.other_translations, master_page
    
    # Test translation_for
    assert_equal en_page, master_page.translation_for("en")
    assert_equal de_page, master_page.translation_for("de")
    assert_equal master_page, master_page.translation_for("cs")
    
    # Test from translation perspective
    assert_equal master_page, en_page.translation_for("cs")
    assert_equal de_page, en_page.translation_for("de")
  end

  test "available and missing translation locales" do
    @website.update!(available_locales: ["cs", "en", "de", "pl"])
    
    master_page = Page.create!(title: "Kontakt", locale: "cs", type: "Content")
    Page.create!(
      title: "Contact", 
      locale: "en", 
      type: "Content",
      translation_group_id: master_page.id
    )
    
    assert_equal ["cs", "en"], master_page.available_translation_locales.sort
    assert_equal ["de", "pl"], master_page.missing_translation_locales.sort
  end
end
