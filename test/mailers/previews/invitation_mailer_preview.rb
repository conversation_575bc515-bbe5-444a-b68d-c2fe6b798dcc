# Preview all emails at http://localhost:3000/rails/mailers/invitation_mailer
class InvitationMailerPreview < ActionMailer::Preview
  # Preview this email at http://localhost:3000/rails/mailers/invitation_mailer/invite
  def invite
    invitation = Invitation.first || create_sample_invitation
    InvitationMailer.invite(invitation)
  end

  private

  def create_sample_invitation
    account = Account.first || Account.create!(name: "Restaurace U Fleku")
    user = User.first || User.create!(
      email: "<EMAIL>",
      name: "<PERSON>",
      password: "password123"
    )

    Invitation.create!(
      email: "<EMAIL>",
      account: account,
      user: user
    )
  end
end
