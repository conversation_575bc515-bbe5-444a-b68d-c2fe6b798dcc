# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

# == Schema Information
#
# Table name: block_containers
#
#  id                         :bigint           not null, primary key
#  alignment                  :string
#  background_overlay_opacity :string
#  container                  :string
#  gap                        :string
#  gap_y                      :string
#  media_alignment            :string
#  padding_x                  :string
#  padding_y                  :string
#  theme                      :string
#  type                       :string
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#  block_id                   :bigint           not null
#
# Indexes
#
#  index_block_containers_on_block_id  (block_id)
#
# Foreign Keys
#
#  fk_rails_...  (block_id => blocks.id)
#
one:
  type: 
  block: one
  theme: MyString
  container: MyString
  padding_x: MyString
  padding_y: MyString
  media_alignment: MyString
  alignment: MyString
  background_overlay_opacity: MyString

two:
  type: 
  block: two
  theme: MyString
  container: MyString
  padding_x: MyString
  padding_y: MyString
  media_alignment: MyString
  alignment: MyString
  background_overlay_opacity: MyString
