# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

# == Schema Information
#
# Table name: layouts
#
#  id                   :bigint           not null, primary key
#  name                 :string
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  main_footer_block_id :bigint
#  main_header_block_id :bigint
#  website_id           :bigint           not null
#
# Indexes
#
#  index_layouts_on_main_footer_block_id  (main_footer_block_id)
#  index_layouts_on_main_header_block_id  (main_header_block_id)
#  index_layouts_on_website_id            (website_id)
#
# Foreign Keys
#
#  fk_rails_...  (main_footer_block_id => blocks.id)
#  fk_rails_...  (main_header_block_id => blocks.id)
#  fk_rails_...  (website_id => websites.id)
#
one:
  name: MyString

two:
  name: MyString
