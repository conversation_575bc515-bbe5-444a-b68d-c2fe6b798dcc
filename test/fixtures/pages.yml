# == Schema Information
#
# Table name: pages
#
#  id                   :bigint           not null, primary key
#  ancestry             :string
#  ancestry_depth       :integer          default(0)
#  is_homepage          :boolean          default(FALSE)
#  link                 :string
#  locale               :string           default("cs")
#  meta_description     :string(500)
#  meta_title           :string
#  position             :integer
#  published_at         :datetime
#  slug                 :string
#  title                :string
#  type                 :string
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  anchor_block_id      :bigint
#  layout_id            :bigint
#  translation_group_id :bigint
#  website_id           :bigint
#
# Indexes
#
#  index_pages_on_ancestry              (ancestry)
#  index_pages_on_anchor_block_id       (anchor_block_id)
#  index_pages_on_layout_id             (layout_id)
#  index_pages_on_translation_group_id  (translation_group_id)
#  index_pages_on_website_id            (website_id)
#
# Foreign Keys
#
#  fk_rails_...  (anchor_block_id => blocks.id)
#  fk_rails_...  (layout_id => layouts.id)
#  fk_rails_...  (translation_group_id => pages.id)
#  fk_rails_...  (website_id => websites.id)
#

one:
  account: one
  title: MyString
  content: MyText
  slug: MyString
  published_at: 2024-05-13 19:46:14

two:
  account: two
  title: MyString
  content: MyText
  slug: MyString
  published_at: 2024-05-13 19:46:14
