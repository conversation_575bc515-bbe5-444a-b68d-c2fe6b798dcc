# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

# == Schema Information
#
# Table name: layout_blocks
#
#  id         :bigint           not null, primary key
#  location   :string
#  position   :integer
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  block_id   :bigint           not null
#  layout_id  :bigint           not null
#
# Indexes
#
#  index_layout_blocks_on_block_id   (block_id)
#  index_layout_blocks_on_layout_id  (layout_id)
#
# Foreign Keys
#
#  fk_rails_...  (block_id => blocks.id)
#  fk_rails_...  (layout_id => layouts.id)
#
one:
  layout: one
  block: one
  position: 1
  location: MyString

two:
  layout: two
  block: two
  position: 1
  location: MyString
