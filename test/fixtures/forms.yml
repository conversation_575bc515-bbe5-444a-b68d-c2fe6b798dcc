# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

# == Schema Information
#
# Table name: forms
#
#  id           :bigint           not null, primary key
#  booking_type :integer
#  description  :text
#  name         :string
#  type         :string
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  user_id      :bigint           not null
#  website_id   :bigint           not null
#
# Indexes
#
#  index_forms_on_user_id     (user_id)
#  index_forms_on_website_id  (website_id)
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#  fk_rails_...  (website_id => websites.id)
#
one:
  name: MyString
  description: MyText
  type: 
  booking_type: 1
  user: one

two:
  name: MyString
  description: MyText
  type: 
  booking_type: 1
  user: two
