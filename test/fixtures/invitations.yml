# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

# == Schema Information
#
# Table name: invitations
#
#  id          :bigint           not null, primary key
#  accepted_at :datetime
#  email       :string
#  token       :string
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  account_id  :bigint           not null
#  user_id     :bigint           not null
#
# Indexes
#
#  index_invitations_on_account_id  (account_id)
#  index_invitations_on_user_id     (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (account_id => accounts.id)
#  fk_rails_...  (user_id => users.id)
#
one:
  email: MyString
  token: MyString
  account: one
  user: one
  accepted_at: 2025-06-28 22:42:33

two:
  email: MyString
  token: MyString
  account: two
  user: two
  accepted_at: 2025-06-28 22:42:33
