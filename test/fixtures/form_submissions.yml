# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

# == Schema Information
#
# Table name: form_submissions
#
#  id         :bigint           not null, primary key
#  data       :jsonb            not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  form_id    :bigint
#  website_id :bigint           not null
#
# Indexes
#
#  index_form_submissions_on_form_id     (form_id)
#  index_form_submissions_on_website_id  (website_id)
#
# Foreign Keys
#
#  fk_rails_...  (form_id => forms.id)
#  fk_rails_...  (website_id => websites.id)
#
one:
  form: one
  website: one
  data: 

two:
  form: two
  website: two
  data: 
