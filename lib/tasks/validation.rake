# frozen_string_literal: true

namespace :validation do
  desc "Projde všechny BlockContainer záznamy a zkontroluje jejich validitu"
  task check_containers: :environment do
    # ... kód pro check_containers zůstáv<PERSON> stejný ...
    puts "▶️  Spouštím kontrolu validity pro všechny BlockContainer záznamy..."
    invalid_count = 0

    BlockContainer.find_each do |container|
      unless container.valid?
        invalid_count += 1
        puts "--------------------------------------------------"
        puts "❌ Nevalidní kontejner: ##{container.id} (Typ: #{container.type}, Blok: #{container.block_id})"
        puts "   Chyby: #{container.errors.full_messages.join(', ')}"
        puts "   Současné hodnoty:"
        container.attributes.each do |attr, value|
          puts "     - #{attr}: #{value}" if value.present? && attr != "type"
        end
      end
    end

    puts "\n✅ Kontrola dokončena."
    if invalid_count.zero?
      puts "🎉 Všechny záznamy jsou validní!"
    else
      puts "🔴 Nalezeno celkem #{invalid_count} nevalidních záznamů."
    end
  end

  desc "Najde nevalidní BlockContainer záznamy a pokusí se je opravit defaultní hodnotou"
  task fix_containers: :environment do
    puts "▶️  Spouštím opravu nevalidních BlockContainer záznamů..."
    fixed_count = 0
    failed_count = 0

    BlockContainer.find_each do |container|
      next if container.valid?

      puts "--------------------------------------------------"
      puts "🔧 Zpracovávám nevalidní kontejner ##{container.id} (Typ: #{container.type})"

      container.errors.attribute_names.uniq.each do |attribute|
        # Zkontrolujeme, jestli pro daný atribut existuje v modelu defaultní hodnota
        next unless container.class.const_defined?(:DEFAULTS) && container.class::DEFAULTS.key?(attribute)

        # ZÍSKÁNÍ VÝCHOZÍ HODNOTY Z NOVÉ KONSTANTY
        default_value = container.class::DEFAULTS[attribute]
        current_value = container[attribute]

        puts "   -> Opravuji '#{attribute}': z '#{current_value}' na '#{default_value}'"
        container[attribute] = default_value
      end

      if container.save
        puts "   ✅ Kontejner ##{container.id} byl úspěšně opraven a uložen."
        fixed_count += 1
      else
        puts "   ❌ CHYBA: Kontejner ##{container.id} se nepodařilo uložit ani po opravě!"
        puts "      Nové chyby: #{container.errors.full_messages.join(', ')}"
        failed_count += 1
      end
    end

    puts "\n✅ Opravný proces dokončen."
    puts "   Opraveno záznamů: #{fixed_count}"
    puts "   Neúspěšných pokusů: #{failed_count}"
  end
end