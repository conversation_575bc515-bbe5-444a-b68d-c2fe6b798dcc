namespace :invitations do
  desc "Clean up expired invitations"
  task cleanup: :environment do
    expired_count = Invitation.expired.count
    Invitation.expired.destroy_all

    puts "Cleaned up #{expired_count} expired invitations"
  end
  
  desc "Show invitation statistics"
  task stats: :environment do
    total = Invitation.count
    pending = Invitation.pending.count
    accepted = Invitation.accepted.count
    expired = Invitation.expired.count

    puts "Invitation Statistics:"
    puts "Total: #{total}"
    puts "Pending: #{pending}"
    puts "Accepted: #{accepted}"
    puts "Expired: #{expired}"
  end
end
