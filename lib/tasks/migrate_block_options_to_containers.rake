namespace :data do
  desc "Migrate block options from jsonb to BlockContainer table"
  task migrate_block_options_to_containers: :environment do
    puts "Starting migration of block options to BlockContainer table..."
    
    # Mapping layer attributes to STI class names
    layer_mappings = {
      'media_layer_attributes' => 'MediaBlockContainer',
      'content_layer_attributes' => 'ContentBlockContainer', 
      'inner_container_layer_attributes' => 'InnerBlockContainer',
      'outer_container_layer_attributes' => 'OuterBlockContainer'
    }
    
    # Get all blocks with options data
    blocks_with_options = Block.where.not(options: {})
    total_blocks = blocks_with_options.count
    
    puts "Found #{total_blocks} blocks with options data to migrate"
    
    migrated_containers = 0
    skipped_blocks = 0
    
    blocks_with_options.find_each.with_index do |block, index|
      puts "Processing block #{block.id} (#{index + 1}/#{total_blocks})"
      
      begin
        # Skip if block already has containers
        if block.block_containers.exists?
          puts "  - Block #{block.id} already has containers, skipping"
          skipped_blocks += 1
          next
        end
        
        # Process each layer type
        layer_mappings.each do |layer_key, container_class_name|
          layer_data = block.options[layer_key]
          
          next if layer_data.blank?
          
          # Create container instance
          container_class = container_class_name.constantize
          container_attributes = {
            block: block,
            type: container_class_name
          }
          
          # Map layer data to container attributes
          layer_data.each do |key, value|
            # Convert key to match container column names
            column_name = key.to_s
            
            # Map specific attributes if needed
            case column_name
            when 'gap'
              # inner_container_layer has 'gap' but container table doesn't have gap column
              # Skip this attribute as it's not in the container schema
              next
            when 'gap_y'
              # gap_y is not in container schema, skip
              next
            end
            
            # Only set attributes that exist as columns in BlockContainer
            if BlockContainer.column_names.include?(column_name)
              container_attributes[column_name] = value
            end
          end
          
          # Create the container
          container = container_class.create!(container_attributes)
          migrated_containers += 1
          
          puts "  - Created #{container_class_name} with attributes: #{container_attributes.except(:block, :type)}"
        end
        
      rescue => e
        puts "  - ERROR processing block #{block.id}: #{e.message}"
        puts "  - Block options: #{block.options.inspect}"
        raise e if Rails.env.development? # Re-raise in development for debugging
      end
    end
    
    puts "\nMigration completed!"
    puts "- Total blocks processed: #{total_blocks}"
    puts "- Blocks skipped (already had containers): #{skipped_blocks}"
    puts "- Containers created: #{migrated_containers}"
    puts "- Blocks successfully migrated: #{total_blocks - skipped_blocks}"
    
    # Verification
    puts "\nVerification:"
    puts "- Total BlockContainers in database: #{BlockContainer.count}"
    puts "- MediaBlockContainer: #{MediaBlockContainer.count}"
    puts "- ContentBlockContainer: #{ContentBlockContainer.count}"
    puts "- InnerBlockContainer: #{InnerBlockContainer.count}"
    puts "- OuterBlockContainer: #{OuterBlockContainer.count}"
  end
end
