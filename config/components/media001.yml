default:
  options:
    name: "media001"               # Původní název bloku
    outer_container_layer:
      theme: "soft"                 # Z původního options.theme
      padding_y: "sm:py-0"          # Z původního options.outer_padding
      padding_x: "sm:px-4"          # Rozumný výchozí padding X
      container: "container-full"   # Z původního options.container
    inner_container_layer:
      theme: "soft"                 # Z původního options.theme
      padding_y: "sm:py-8"          # Z původního options.padding
      padding_x: "sm:px-4"          # Rozumný výchozí padding X
      container: "container-lg"     # Z původního options.content_container
    content_layer:
      theme: "soft"                 # Z původního options.theme
      padding_y: "py-4"             # Výchozí Y padding pro obsah
      padding_x: "px-4"             # Výchozí X padding pro obsah
      container: "max-w-prose"      # <PERSON><PERSON><PERSON><PERSON><PERSON> vý<PERSON><PERSON><PERSON> kontejner pro textový obsah
      gap_y: "gap-y-8"              # Z původního options.gap_y
      alignment: "left"             # Z původního options.alignment
  media_options:
    theme: "soft"                 # Z původního options.theme
    padding_y: "py-0"             # Výchozí Y padding pro média
    padding_x: "px-0"             # Výchozí X padding pro média
    gap_y: "gap-y-0"              # Výchozí vertikální mezera v media vrstvě
    alignment: "center"           # Běžné výchozí zarovnání pro mediální blok
    posts_limit: 3                # Z původního media.posts_limit
    position: "right"             # Z původního media.position
    gap: 4                        # Z původního media.gap
    type: "gallery"               # Z původního media.type
    layout: "grid"                # Z původního media.layout
  media_items: # Přesunuto na nejvyšší úroveň, zachovává původní data
    - image_url: "https://picsum.photos/300/400"
      title: "Image 1"
    - image_url: "https://picsum.photos/300/400"
      title: "Image 2"
    - image_url: "https://picsum.photos/300/400"
      title: "Image 3"
    - image_url: "https://picsum.photos/300/400"
      title: "Image 4"
    - image_url: "https://picsum.photos/300/400"
      title: "Image 5"
    - image_url: "https://picsum.photos/300/400"
      title: "Image 6"
    - image_url: "https://picsum.photos/300/400"
      title: "Image 7"
    - image_url: "https://picsum.photos/300/400"
      title: "Image 8"
  controls: # Zůstává na nejvyšší úrovni, obsah zkopírován
    - type: "BlockControls::Heading"
      position: 1
      text: "Hello World!"
      options:
        heading_type: "h2"
        pre_header: ""
    - type: "BlockControls::Paragraph"
      position: 2
      text: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc a lectus nisi. Vestibulum euismod feugiat neque a ullamcorper. Aliquam erat volutpat. Proin finibus vestibulum metus, vel sodales nibh efficitur ornare. Donec lacinia sapien posuere eros blandit, quis volutpat leo rhoncus. Nunc quis finibus diam, et scelerisque libero. Morbi malesuada ipsum et felis ultricies auctor."