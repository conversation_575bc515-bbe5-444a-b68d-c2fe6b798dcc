# == Schema Information
#
# Table name: services
#
#  id           :bigint           not null, primary key
#  options      :jsonb
#  processed_at :datetime
#  type         :string
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  website_id   :bigint           not null
#
# Indexes
#
#  index_services_on_website_id  (website_id)
#
# Foreign Keys
#
#  fk_rails_...  (website_id => websites.id)
#

module Integrations
  class GoogleMaps < Service
    store_accessor :options, :place_url, :place_id, :import_reviews, :active

    validates :place_url, presence: { message: "URL na Google Maps je povinná" }, if: :active?
    validates :place_url, format: {
      with: /\Ahttps?:\/\/(www\.)?google\.(com|cz)\/maps/,
      message: "URL musí být platná Google Maps adresa",
      allow_blank: true
    }

    def self.permitted_params
      [ :place_url, :place_id, :import_reviews, :active ]
    end

    def active?
      # Převod na boolean - active může být "1", "0", true, false, nil
      ActiveModel::Type::Boolean.new.cast(active)
    end
  end
end
