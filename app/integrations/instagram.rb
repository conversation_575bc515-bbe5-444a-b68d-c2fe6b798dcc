# == Schema Information
#
# Table name: services
#
#  id           :bigint           not null, primary key
#  options      :jsonb
#  processed_at :datetime
#  type         :string
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  website_id   :bigint           not null
#
# Indexes
#
#  index_services_on_website_id  (website_id)
#
# Foreign Keys
#
#  fk_rails_...  (website_id => websites.id)
#

module Integrations
  class Instagram < Service
    store_accessor :options, :instagram_url

    validates :instagram_url, presence: { message: "URL na Instagram profil je povinná" }
    validates :instagram_url, format: {
      with: /\Ahttps?:\/\/(www\.)?instagram\.com\/[a-zA-Z0-9._]+\/?/,
      message: "URL musí být platná Instagram adresa (např. https://www.instagram.com/uzivatelske_jmeno/)",
      allow_blank: true
    }

    def self.permitted_params
      [ :instagram_url ]
    end
  end
end
