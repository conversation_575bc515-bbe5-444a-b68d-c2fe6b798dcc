class PricingItemsController < ApplicationController
  include PageContextBuilder

  def show
    @layout = Layout.take

    @layout_models = LayoutCompositionService.new(@layout).call

    @pricing_item = PricingItem.i18n
                               .includes(:image_attachment, pricing_section: :pricing)
                               .find_by!(slug: params[:slug])

    build_view_context(type: :frontend, locale: I18n.locale)

    @layout_components = {
      main_header: @layout_models[:main_header] ? BlockFactory.create_presenter(source: @layout_models[:main_header], view_context: @view_context).component : nil,
      main_footer: @layout_models[:main_footer] ? BlockFactory.create_presenter(source: @layout_models[:main_footer], view_context: @view_context).component : nil
    }
  end
end
