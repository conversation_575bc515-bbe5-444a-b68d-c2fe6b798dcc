# frozen_string_literal: true

  module BlockManagement
    extend ActiveSupport::Concern

    def update
      if @block.update(block_params)
        redirect_to after_update_path
      else
        # Zde bychom měli spr<PERSON>ně renderovat 'edit' s chybami
        render :edit, status: :unprocessable_entity
      end
    end

    def hide
      @block.update(hidden_at: @block.hidden? ? nil : Time.now)
      redirect_to after_update_path
    end

    def destroy
      @block.destroy
      redirect_to after_update_path
    end

    private

    def block_params
      params.require(:block).permit(
        :background_image, :height, :pricing_id, :media_collection_id,
        options: {}, pricing_options: {},
        block_containers_attributes: [
          :id,
          :type,
          :theme,
          :container,
          :padding_y,
          :padding_x,
          :background_overlay_opacity,
          :gap,
          :gap_y,
          :media_alignment,
          :alignment
        ]
      ).tap do |whitelisted|
        if params.dig(:block, :controls_attributes)
          whitelisted[:controls_attributes] = block_controls_params
        end
      end
    end

    def block_controls_params
      permitted_controls = {}
      params[:block][:controls_attributes].each do |key, attributes|
        type = attributes[:type]
        klass = type.safe_constantize

        permitted_attributes = if klass && klass < BlockControl
                                 klass.permitted_attributes + [:id, :_destroy]
                               else
                                 [:id, :_destroy]
                               end
        permitted_controls[key] = attributes.permit(*permitted_attributes)
      end
      permitted_controls
    end
  end

