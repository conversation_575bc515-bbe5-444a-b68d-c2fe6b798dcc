class FormSubmissionsController < ApplicationController
  before_action :set_form

  def create
    @form_submission = @form.form_submissions.new(form_submission_params)

    if @form_submission.save

    else
      flash.now[:alert] = 'Formulář se nepo<PERSON>, zkontrolujte prosím vyplněné <PERSON>.'
      render 'pages/contact', status: :unprocessable_entity
    end
  end

  private

  def set_form
    @form = Form.find(params[:form_id])
  end

  def form_submission_params
    params.require(:form_submission).permit(data: {})
  end
end