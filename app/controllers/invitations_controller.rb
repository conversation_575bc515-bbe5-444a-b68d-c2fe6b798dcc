class InvitationsController < ApplicationController
  before_action :find_invitation, only: [:accept, :register]

  def accept
    if @invitation.accepted?
      redirect_to new_session_path, alert: "Tato pozvánka již byla použita."
      return
    end

    if @invitation.expired?
      redirect_to new_session_path, alert: "Tato pozvánka již vypršela."
      return
    end

    @existing_user = User.find_by(email: @invitation.email)
    
    if @existing_user
      # Uživatel již existuje - vytvoříme propojení a označíme pozvánku jako přijatou
      unless @existing_user.accounts.include?(@invitation.account)
        @existing_user.account_users.create!(account: @invitation.account)
      end
      @invitation.accept!
      
      redirect_to new_session_path, notice: "Úspěšně jste byli přidáni do správy účtu. Můžete se nyní přihlásit."
    else
      # Uživatel neexistuje - zobrazíme registrační formulář
      @user = User.new(email: @invitation.email)
    end
  end

  def register
    if @invitation.accepted?
      redirect_to new_session_path, alert: "Tato pozvánka již byla použ<PERSON>."
      return
    end

    if @invitation.expired?
      redirect_to new_session_path, alert: "Tato pozvánka již vypršela."
      return
    end

    @user = User.new(user_params)
    @user.email = @invitation.email # Email je předvyplněný a needitovatelný

    if @user.save
      # Vytvoříme propojení s účtem
      @user.account_users.create!(account: @invitation.account)
      @invitation.accept!

      redirect_to new_session_path, notice: "Váš účet byl úspěšně vytvořen. Můžete se nyní přihlásit."
    else
      @existing_user = nil # Zajistíme, že se zobrazí registrační formulář
      render :accept, status: :unprocessable_entity
    end
  end

  private

  def find_invitation
    @invitation = Invitation.find_by!(token: params[:token])
  rescue ActiveRecord::RecordNotFound
    redirect_to new_session_path, alert: "Pozvánka nebyla nalezena nebo je neplatná."
  end

  def user_params
    params.require(:user).permit(:first_name, :last_name, :name, :password, :password_confirmation)
  end
end
