class ApplicationController < ActionController::Base
  include SetCurrentRequestDetails

  around_action :switch_locale
  before_action :remove_default_locale_from_url
  before_action :set_opening_hours
  before_action :set_theme

  helper_method :opening_hours

  private

  def set_theme
    @form = Form.take
    @form_submission = @form.form_submissions.new

    if current_tenant&.theme&.any? && false
      @theme_colors = current_tenant.theme_colors_with_defaults
      @template_settings = current_tenant.template_settings_with_defaults
      @theme ||= current_tenant.current_template
      @colors ||= @theme_colors
    else
      @theme ||= Rails.application.config.x.themes[:radka]
      @colors ||= @theme[:colors]
    end
  end

  def set_opening_hours
    return unless current_tenant

    opening_hours = current_tenant.opening_hours.for_week
    @specific_dates = group_specific_dates(opening_hours[:specific_dates])
    @generic_days = index_generic_days(opening_hours[:generic_days])
  end

  def group_specific_dates(specific_dates)
    specific_dates.group_by { |opening_hour| opening_hour.date.to_date }
  end

  def index_generic_days(generic_days)
    generic_days.index_by(&:day)
  end

  def remove_default_locale_from_url
    return unless request.get? && Current.website.locale == params[:locale]

    redirect_to request.path_parameters.merge(locale: nil)
  end

  def switch_locale(&action)
    I18n.with_locale(params[:locale] || Current.website.locale, &action)
  end

  def default_url_options
    { locale: locale_for_default_url_options }
  end

  def locale_for_default_url_options
    I18n.locale.to_s == Current.website.locale ? nil : I18n.locale
  end
end
