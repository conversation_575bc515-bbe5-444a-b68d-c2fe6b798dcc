class Admin::Pricing::PricingItemsController < Admin::ApplicationController
  before_action :set_pricing_item

  def delete_image
    if @pricing_item.image.attached?
      @pricing_item.image.purge
    end
  end

  def sort
    @pricing_item.update(position: params[:position].to_i)
    head :ok
  end

  def edit
    add_breadcrumb "Služby", admin_pricing_index_path
    add_breadcrumb @pricing_item.pricing.name, edit_admin_pricing_path(@pricing_item.pricing)
    add_breadcrumb @pricing_item.name
  end

  # Uloží změny z formuláře
  def update
    if @pricing_item.update(pricing_item_params)
      redirect_to edit_admin_pricing_item_path(@pricing_item), notice: 'Detail služby byl úspěšně ulo<PERSON>en.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  private

  def set_pricing_item
    @pricing_item = PricingItem.find(params[:id])
  end

  def pricing_item_params
    mobility_attributes = PricingItem.mobility_attributes.flat_map do |attr|
      I18n.available_locales.map { |locale| "#{attr}_#{locale}" }
    end

    standard_attributes = [:name, :description, :price, :price_eur, :price_textual, :is_price_textual, :duration, :image, :detail_layout_variant]

    params.require(:pricing_item).permit(standard_attributes + mobility_attributes)
  end
end
