# app/controllers/admin/content/layouts_controller.rb
module Admin
  class Content::LayoutsController < Content::ContentController
    before_action :set_layout, only: [:edit, :update, :destroy]

    def index
      @layouts = Layout.all.order(:name)

      add_breadcrumb "Šablony"
    end

    def new
      @layout = Layout.new
    end

    def create
      @layout = Layout.new(layout_params)
      if @layout.save
        redirect_to edit_admin_content_layout_path(@layout), notice: "Layout byl ú<PERSON> vytvořen."
      else
        render :new, status: :unprocessable_entity
      end
    end

    def edit
      @available_blocks = Block.order(:name)
    end

    def update
      if @layout.update(layout_params)
        redirect_to edit_admin_content_layout_path(@layout), notice: "Layout byl úspěšně upraven."
      else
        @available_blocks = Block.order(:name) # Musíme znovu načíst pro render formuláře
        render :edit, status: :unprocessable_entity
      end
    end

    def destroy
      @layout.destroy
      redirect_to admin_content_layouts_path, notice: "Layout byl s<PERSON>."
    end

    private

    def set_layout
      @layout = Layout.find(params[:id])
    end

    # Povolíme parametry pro jméno a ID singulárních blok<PERSON>
    def layout_params
      params.require(:layout).permit(:name, :main_header_block_id, :main_footer_block_id)
    end
  end
end
