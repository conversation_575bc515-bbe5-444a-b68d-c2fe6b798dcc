# frozen_string_literal: true

module Admin
  class Content::LayoutBlocksController < Content::ContentController
    include BlockManagement
    include PageContextBuilder

    layout "editor"

    before_action :set_layout
    before_action :set_block, only: [:edit, :update, :hide, :destroy, :sort]

    def index
      @owner = @layout

      @layout_models = LayoutCompositionService.new(@layout).call

      build_view_context(type: :admin, locale: Current.website.locale)

      @layout_components = {
        main_header: @layout_models[:main_header] ? BlockFactory.create_presenter(source: @layout_models[:main_header], view_context: @view_context).component : nil,
        global_headers: @layout_models[:global_headers].map { |m| BlockFactory.create_presenter(source: m, view_context: @view_context).component },
        global_footers: @layout_models[:global_footers].map { |m| BlockFactory.create_presenter(source: m, view_context: @view_context).component },
        main_footer: @layout_models[:main_footer] ? BlockFactory.create_presenter(source: @layout_models[:main_footer], view_context: @view_context).component : nil
      }
    end

    def edit
      @owner = @layout
      set_layout_locale
      @controls = @block.controls
      @locale = @layout_locale

      render "admin/content/page_blocks/edit"
    end

    def new
      @owner = @layout
      @block = Block.new

      if params[:add].present?
        block_type = params[:add].to_sym

        build_view_context(type: :admin, locale: Current.website.locale)

        if block_type == :references
          existing_blocks = Block.joins(:layout_blocks)
                                 .where.not(layout_blocks: { layout_id: @layout.id })
                                 .distinct
                                 .includes(:controls, :media_collection)

          @block_components = existing_blocks.map { |block| BlockFactory.create_presenter(source: block, view_context: @view_context).component }.compact
        else
          @block_components = BlockFactory.build_by_type(block_type, view_context: @view_context)
        end
      end

      render "admin/content/page_blocks/new"
    end

    def create
      build_view_context(type: :admin)

      block_data = BlockFactory::build_block_data(source: params.dig(:block_type), locale: :cs)

      new_block = BlockCreator.create(block_data: block_data, website: Current.website)

      @layout.layout_blocks.create(block: new_block, location: params[:location])

      if params[:location] == "main_header"
        @layout.update(main_header_block: new_block)
      end

      if params[:location] == "main_footer"
        @layout.update(main_footer_block: new_block)
      end

      redirect_to admin_content_layout_blocks_path(@layout)
    end

    def create_reference
      reference_block = Block.find(params[:block_id])

      # Vytvoříme nový LayoutBlock záznam pro referenci
      layout_block = @layout.layout_blocks.build(block: reference_block, location: params[:location])

      if layout_block.save
        # Zajistíme, že existují BlockControls pro aktuální lokalizaci layoutu
        set_layout_locale
        ensure_block_controls_for_locale(reference_block, @layout_locale)
        redirect_to admin_content_layout_blocks_path(@layout)
      else
        redirect_to admin_content_layout_blocks_path(@layout), alert: "Nepodařilo se přidat referenci bloku."
      end
    end

    def sort
      layout_block = @layout.layout_blocks.find_by(block: @block)
      redirect_to after_update_path
    end

    private

    def set_layout
      @layout = Layout.find(params[:layout_id])
    end

    def set_block
      @block = Block.find(params[:id])
    end

    def owner
      @owner = @layout
    end

    def after_update_path
      admin_content_layout_blocks_path(@layout)
    end

    def set_layout_locale
      @layout_locale ||= available_locales.include?(params[:locale]) ? params[:locale] : Current.website.locale
    end

    def ensure_block_controls_for_locale(block, locale)
      # Zkontrolujeme, zda existují controls pro danou lokalizaci
      existing_controls = block.controls.for_locale(locale)

      if existing_controls.empty?
        # Najdeme controls z jiné lokalizace jako šablonu
        template_controls = block.controls.where.not(locale: locale).group_by(&:position)

        template_controls.each do |position, controls|
          # Vezmeme první control z této pozice jako šablonu
          template_control = controls.first

          # Vytvoříme nový control pro požadovanou lokalizaci
          block.controls.create!(
            type: template_control.type,
            position: position,
            locale: locale,
            text: template_control.text,
            options: template_control.options,
            classes: template_control.classes,
            styles: template_control.styles,
            content: template_control.content,
            container: template_control.container
          )
        end
      end
    end
  end
end
