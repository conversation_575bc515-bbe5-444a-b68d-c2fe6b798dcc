class Admin::Settings::UsersController < Admin::ApplicationController
  layout "admin/settings"

  def index
    @users = current_tenant.account.users.includes(:account_users).order(:name, :email)
    @invitations = current_tenant.account.invitations.includes(:user).recent
    add_breadcrumb "Nastavení webu", admin_settings_path
    add_breadcrumb "Správci"
  end

  def create_invitation
    @invitation = current_tenant.account.invitations.build(invitation_params)
    @invitation.user = current_user

    if @invitation.save
      InvitationMailer.invite(@invitation).deliver_now
      redirect_to admin_settings_users_path, notice: "<PERSON>zvánka byla úspěšně odeslána na #{@invitation.email}."
    else
      @users = current_tenant.account.users.includes(:account_users).order(:name, :email)
      @invitations = current_tenant.account.invitations.includes(:user).recent
      add_breadcrumb "Nastavení webu", admin_settings_path
      add_breadcrumb "Správci"
      render :index, status: :unprocessable_entity
    end
  end

  def destroy
    @user = current_tenant.account.users.find(params[:id])
    
    if @user == current_user
      redirect_to admin_settings_users_path, alert: "Nemůžete odebrat sami sebe."
      return
    end

    account_user = current_tenant.account.account_users.find_by(user: @user)
    if account_user
      account_user.destroy
      redirect_to admin_settings_users_path, notice: "Správce #{@user.name || @user.email} byl odebrán."
    else
      redirect_to admin_settings_users_path, alert: "Správce nebyl nalezen."
    end
  end

  private

  def invitation_params
    params.require(:invitation).permit(:email)
  end
end
