class Admin::Settings::DomainsController < Admin::ApplicationController
  layout "admin/settings"

  def show
    add_breadcrumb "Nastavení webu", admin_settings_path
    add_breadcrumb "Doména"
  end

  def update
    if current_tenant.update(domain_params)
      redirect_to admin_settings_domain_path, notice: "Nastavení domény bylo <PERSON>."
    else
      render :show, status: :unprocessable_entity
    end
  end

  private

  def domain_params
    params.require(:website).permit(:domain, :use_custom_domain)
  end
end
