class Admin::PricingController < Admin::ApplicationController
  def index
    add_breadcrumb "Ceník"

    @pricing_by_locale = Pricing.active_or_future.includes(pricing_sections: :pricing_items).group_by(&:locale)

    @pricing = Pricing.active_or_future.order(position: :asc)
    @expired_menus = Pricing.expired.order(position: :asc)
  end

  def new
    @pricing = Pricing.new
  end

  def create
    @pricing = current_tenant.pricing.new(pricing_params)

    if @pricing.save
      redirect_to edit_admin_pricing_path(@pricing), notice: "Ceník byl úspěšně vytvořen"
    else
      render :new, status: :unprocessable_entity
    end
  end

  def sort
    @pricing = Pricing.find(params[:id])

    @pricing.update position: params[:position].to_i
  end

  def edit
    @pricing = Pricing.find(params[:id])

    add_breadcrumb "Ceník", admin_pricing_index_path
    add_breadcrumb @pricing.name
  end

  def update
    @pricing = Pricing.find(params[:id])

    if @pricing.update(pricing_params)
      redirect_to edit_admin_pricing_path(@pricing), notice: "<PERSON><PERSON> bylo <PERSON> ul<PERSON>"
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @pricing = Pricing.find(params[:id]).destroy

    redirect_to admin_pricing_path, notice: "Ceník byl úspěšně odstraněn"
  end

  def upload_image
    blob = ActiveStorage::Blob.create_and_upload!(
      io: params[:image],
      filename: params[:image].original_filename,
      content_type: params[:image].content_type
    )

    if blob.persisted?
      variant = blob.variant(resize_to_limit: [1024, nil])

      processed_variant = variant.processed

      render json: { url: url_for(processed_variant) }, status: :created
    else
      render json: { error: "Failed to upload image" }, status: :unprocessable_entity
    end
  end

  private

  def pricing_params
    params.require(:pricing).permit(
      :name, :valid_from, :valid_to, :pricing_type,
      pricing_sections_attributes: [
        :id,
        :name,
        :_destroy,
        {
          pricing_items_attributes: [
            :id,
            :name,
            :price,
            :price_eur,
            :description,
            :duration,
            :image,
            :_destroy,
            :price_textual,
            :is_price_textual
          ]
        }
      ]
    )
  end
end
