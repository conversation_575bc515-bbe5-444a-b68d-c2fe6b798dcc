class Admin::ServicesController < Admin::ApplicationController
  before_action :set_breadcrumbs
  before_action :set_service_type, only: %i[edit create update]

  def index
  end

  def edit
    return redirect_to admin_services_path, alert: "Neplatný typ služby" unless @service_type

    add_breadcrumb @service_type[:name]

    @service = Service.find_by_type(@service_type[:class_name]) || @service_class.new
    @service_name = params[:id].tr("-", "_")
  end

  def create
    return redirect_to admin_services_path, alert: "Neplatný typ služby" unless @service_type

    @service = @service_class.new(service_params)
    @service.website = current_tenant
    @service.type = @service_type[:class_name]

    if @service.save
      redirect_to edit_admin_service_path(@service_type[:slug]), notice: "Nastavení bylo uloženo"
    else
      @service_name = params[:id].tr("-", "_")
      render :edit, status: :unprocessable_entity
    end
  end

  def update
    return redirect_to admin_services_path, alert: "Neplatný typ služby" unless @service_type

    @service = Service.find_by_type(@service_type[:class_name])

    if @service
      if @service.update(service_params)
        redirect_to edit_admin_service_path(params[:id]), notice: "Nastavení bylo uloženo"
      else
        @service_name = params[:id].tr("-", "_")
        render :edit, status: :unprocessable_entity
      end
    else
      # Vytvoříme nový service pokud neexistuje
      @service = @service_class.new(service_params)
      @service.website = current_tenant
      @service.type = @service_type[:class_name]

      if @service.save
        redirect_to edit_admin_service_path(params[:id]), notice: "Nastavení bylo uloženo"
      else
        @service_name = params[:id].tr("-", "_")
        render :edit, status: :unprocessable_entity
      end
    end
  end

  private

  def set_service_type
    @service_type = Service.fetch_by_slug(params[:id])
    @service_class = @service_type[:class_name].constantize if @service_type
  rescue NameError => e
    Rails.logger.error "Service class not found: #{e.message}"
    @service_type = nil
    @service_class = nil
  end

  def service_params
    # Rails form_with model: @service odesílá parametry s klíčem podle názvu modelu
    model_name = @service_class.model_name.param_key
    if params[model_name].present?
      params.require(model_name).permit(*@service_class.permitted_params)
    else
      # Fallback pro případy kdy jsou parametry na top level
      params.permit(*@service_class.permitted_params)
    end
  end

  def set_breadcrumbs
    add_breadcrumb "Propojení", (admin_services_path if action_name != "index")
  end
end
