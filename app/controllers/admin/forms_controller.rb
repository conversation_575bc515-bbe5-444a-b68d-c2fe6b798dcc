# app/controllers/admin/forms_controller.rb
class Admin::FormsController < Admin::ApplicationController
  def index
    @forms = current_tenant.forms.all

    add_breadcrumb "Formuláře"
  end

  def new
    @form = current_tenant.forms.new

    add_breadcrumb "Formuláře", admin_forms_path
    add_breadcrumb "Nový formulář"
  end

  def edit
    @form = current_tenant.forms.find(params[:id])
  end

  def create
    @form = current_tenant.forms.new(form_params)
    @form.user = current_user
    if @form.save
      redirect_to edit_admin_form_path(@form), notice: '<PERSON>ul<PERSON><PERSON> byl úspěšně vytvořen.'
    else
      render :new, status: :unprocessable_entity
    end
  end

  def update
    @form = current_tenant.forms.find(params[:id])
    if @form.update(form_params)
      redirect_to edit_admin_form_path(@form), notice: '<PERSON><PERSON><PERSON><PERSON> byl úspěšně upraven.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @form = current_tenant.forms.find(params[:id])
    @form.destroy
    redirect_to admin_forms_path, notice: '<PERSON><PERSON><PERSON><PERSON> byl s<PERSON>.'
  end

  private

  def form_params
    mobility_attributes = FormField.mobility_attributes.flat_map do |attr|
      I18n.available_locales.map { |locale| "#{attr}_#{locale}" }
    end

    params.require(:form).permit(
      :name, :description, :type,
      form_fields_attributes: [
        :id, :_destroy, :position, :field_type, :required, :options,
        mobility_attributes
      ]
    )
  end
end