import React from 'react';
import PropTypes from 'prop-types';
import { EditorContent, useEditor } from '@tiptap/react';
import { useDebouncedCallback } from 'use-debounce';

// Základn<PERSON>
import StarterKit from '@tiptap/starter-kit';
import TextStyle from '@tiptap/extension-text-style';
import Underline from '@tiptap/extension-underline';
import Highlight from '@tiptap/extension-highlight';
import { Color } from '@tiptap/extension-color'; // Potřeba pro barvu textu
import { ClassStyle } from './ClassStyle.jsx';

// Nová rozšíření
import Link from '@tiptap/extension-link';
import Image from '@tiptap/extension-image';

// Nový toolbar
import ArticleEditorToolbar from './ArticleEditorToolbar.jsx';

const ArticleEditor = ({ initialContent = '', onUpdateHTML, onUpdateJSON, editorClassName = '', blockTheme }) => {
    const debouncedUpdate = useDebouncedCallback(({ editor }) => {
        if (onUpdateHTML) onUpdateHTML(editor.getHTML());
        if (onUpdateJSON) onUpdateJSON(editor.getJSON());
    }, 1000);

    const editor = useEditor({
        // Seznam všech rozšíření pro editor
        extensions: [
            ClassStyle,
            StarterKit.configure({
                // Můžete zde konfigurovat StarterKit, pokud něco nechcete
                // např. heading: { levels: [1, 2, 3] }
            }),
            TextStyle,
            Color, // Přidáno pro podporu TextColorPopover
            Highlight.configure({ multicolor: true }),
            Underline,
            Image, // Povolí vkládání obrázků
            Link.configure({
                // Odkazy se budou otevírat v novém okně
                openOnClick: false, // Doporučeno false pro editační režim
                autolink: true, // Automaticky převede text na odkaz
            }),
        ],
        // Vlastnosti editoru
        editorProps: {
            attributes: {
                class: `focus:outline-none px-5 ${editorClassName}`,
            },
        },
        content: initialContent,
        onUpdate: (props) => {
            debouncedUpdate(props);
        },
    });

    return (
        <div className="tiptap-article-editor-wrapper border border-gray-300">
            {/* Pevný panel nástrojů nahoře */}
            <ArticleEditorToolbar editor={editor} blockTheme={blockTheme} />

            {/* Samotný obsah editoru */}
            <EditorContent editor={editor} />
        </div>
    );
};

ArticleEditor.propTypes = {
    initialContent: PropTypes.string,
    onUpdateHTML: PropTypes.func,
    onUpdateJSON: PropTypes.func,
    editorClassName: PropTypes.string,
    blockTheme: PropTypes.string
};

export default ArticleEditor;