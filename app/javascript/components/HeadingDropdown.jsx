// app/javascript/components/HeadingDropdown.jsx

import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';

const HeadingDropdown = ({ editor }) => {
    // Stav pro sledování, zda je dropdown otevřený
    const [isOpen, setIsOpen] = useState(false);
    // Ref pro hlavní kontejner dropdownu, abychom mohli detekovat kliknutí mimo něj
    const dropdownRef = useRef(null);

    // Tento useEffect se postará o zavření dropdownu, kdy<PERSON> uživatel klikne mimo něj
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setIsOpen(false);
            }
        };
        // Přidáme listener, když se komponenta načte
        document.addEventListener('mousedown', handleClickOutside);
        // Odstraníme listener, k<PERSON><PERSON> se komponenta odpojí, abychom předešli memory leakům
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [dropdownRef]);

    // Pomocná funkce pro zjištění aktuálně aktivního stylu
    const getActiveHeading = () => {
        if (editor.isActive('heading', { level: 1 })) return 'H1';
        if (editor.isActive('heading', { level: 2 })) return 'H2';
        if (editor.isActive('heading', { level: 3 })) return 'H3';
        if (editor.isActive('heading', { level: 4 })) return 'H4';
        if (editor.isActive('heading', { level: 5 })) return 'H5';
        return 'Text';
    };

    const headingOptions = [
        { level: 1, label: 'Nadpis 1' },
        { level: 2, label: 'Nadpis 2' },
        { level: 3, label: 'Nadpis 3' },
        { level: 4, label: 'Nadpis 4' },
        { level: 5, label: 'Nadpis 5' },
    ];

    const handleSelect = (level) => {
        if (level === 0) { // 0 bude reprezentovat odstavec
            editor.chain().focus().setParagraph().run();
        } else {
            editor.chain().focus().toggleHeading({ level }).run();
        }
        setIsOpen(false); // Po výběru zavřeme dropdown
    };

    return (
        <div className="relative" ref={dropdownRef}>
            {/* Hlavní tlačítko, které zobrazuje aktuální stav a otevírá dropdown */}
            <button
                type="button"
                onClick={() => setIsOpen(!isOpen)}
                className="flex items-center space-x-1 rounded-md px-2 py-1.5 text-sm font-semibold hover:bg-gray-100"
            >
                <span>{getActiveHeading()}</span>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" className="size-4">
                    <path fillRule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clipRule="evenodd" />
                </svg>
            </button>

            {/* Samotné menu, které se zobrazí/skryje na základě stavu `isOpen` */}
            {isOpen && (
                <div className="absolute top-full mt-1 z-20 w-40 rounded-md border border-gray-200 bg-white p-1 shadow-lg">
                    {/* Obyčejný text (odstavec) */}
                    <button
                        type="button"
                        onClick={() => handleSelect(0)}
                        className={`w-full text-left px-3 py-1.5 text-sm rounded-md ${editor.isActive('paragraph') ? 'bg-gray-200' : 'hover:bg-gray-100'}`}
                    >
                        Běžný text
                    </button>

                    {/* Možnosti nadpisů */}
                    {headingOptions.map(opt => (
                        <button
                            key={opt.level}
                            type="button"
                            onClick={() => handleSelect(opt.level)}
                            className={`w-full text-left px-3 py-1.5 text-sm rounded-md ${editor.isActive('heading', { level: opt.level }) ? 'bg-gray-200' : 'hover:bg-gray-100'}`}
                        >
                            <span className={`font-bold text-h${opt.level}`}>{opt.label}</span>
                        </button>
                    ))}
                </div>
            )}
        </div>
    );
};

HeadingDropdown.propTypes = {
    editor: PropTypes.object.isRequired,
};

export default HeadingDropdown;