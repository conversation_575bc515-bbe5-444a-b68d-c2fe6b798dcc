// app/javascript/components/ArticleEditorToolbar.jsx

import React, { useCallback, useRef, useState } from 'react'; // <-- ZMĚNA: Přidán import useRef
import PropTypes from 'prop-types';
import HeadingDropdown from './HeadingDropdown.jsx';
import HighlightPopover from './HighlightPopover.jsx';
import TextColorPopover from './TextColorPopover.jsx';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLink, faImage, faQuoteLeft, faListUl, faListOl, faMinus } from '@fortawesome/free-solid-svg-icons';
import { FetchRequest } from '@rails/request.js';

const ArticleEditorToolbar = ({ editor, blockTheme }) => {
    const fileInputRef = useRef(null);

    const [isUploading, setIsUploading] = useState(false);

    if (!editor) {
        return null;
    }

    // Funkce pro vložení od<PERSON> (zůstává beze změny)
    const setLink = useCallback(() => {
        const previousUrl = editor.getAttributes('link').href;
        const url = window.prompt('URL', previousUrl);

        if (url === null) {
            return;
        }

        if (url === '') {
            editor.chain().focus().extendMarkRange('link').unsetLink().run();
            return;
        }

        editor.chain().focus().extendMarkRange('link').setLink({ href: url }).run();
    }, [editor]);

    // --- ZMĚNA: Původní funkce `addImage` je nahrazena ---

    // Tato funkce se spustí po výběru souboru v dialogovém okně.
    const handleImageUpload = useCallback(async (event) => {
        const file = event.target.files?.[0];
        if (!file) {
            return;
        }

        setIsUploading(true);

        const formData = new FormData();
        formData.append('image', file);

        try {
            // 1. Vytvoříme novou instanci požadavku
            const request = new FetchRequest('POST', '/admin/6/pricing/upload_image', {
                body: formData
            });

            // 2. Provedeme požadavek pomocí metody .perform()
            const response = await request.perform();

            if (response.ok) {
                const data = await response.json;
                if (data.url) {
                    editor.chain().focus().setImage({ src: data.url }).run();
                }
            } else {
                console.error('Chyba při nahrávání obrázku:', await response.text);
                alert('Nahrávání obrázku selhalo.');
            }
        } catch (error) {
            console.error('Došlo k chybě sítě:', error);
            alert('Došlo k chybě sítě. Zkontrolujte konzoli pro více informací.');
        } finally {
            setIsUploading(false);

            if (fileInputRef.current) {
                fileInputRef.current.value = '';
            }
        }
    }, [editor]);

    return (
        <div data-theme={blockTheme} className="tiptap-toolbar text-black bg-white p-2 border-b border-gray-200 sticky top-0 z-10">
            <div className="flex flex-wrap items-center gap-x-3 gap-y-1">
                {/* ... ostatní skupiny tlačítek zůstávají beze změny ... */}
                <div className="flex items-center border-r border-gray-300 pr-3">
                    <HeadingDropdown editor={editor} />
                </div>
                <div className="flex items-center border-r border-gray-300 pr-3 space-x-1">
                    <button type="button" onClick={() => editor.chain().focus().toggleBold().run()} className={`toggle-button w-8 h-8 font-bold rounded-md ${editor.isActive('bold') ? 'bg-gray-200' : 'hover:bg-gray-100'}`} title="Tučné (Ctrl+B)">B</button>
                    <button type="button" onClick={() => editor.chain().focus().toggleItalic().run()} className={`toggle-button w-8 h-8 italic rounded-md ${editor.isActive('italic') ? 'bg-gray-200' : 'hover:bg-gray-100'}`} title="Kurzíva (Ctrl+I)">I</button>
                    <button type="button" onClick={() => editor.chain().focus().toggleUnderline().run()} className={`toggle-button w-8 h-8 underline rounded-md ${editor.isActive('underline') ? 'bg-gray-200' : 'hover:bg-gray-100'}`} title="Podtržené (Ctrl+U)">U</button>
                </div>
                <div className="flex items-center border-r border-gray-300 pr-3 space-x-1">
                    <HighlightPopover editor={editor} />
                    <TextColorPopover editor={editor} data-block-theme={blockTheme} />
                </div>
                <div className="flex items-center border-r border-gray-300 pr-3 space-x-1">
                    <button type="button" onClick={() => editor.chain().focus().toggleBulletList().run()} className={`toggle-button w-8 h-8 rounded-md ${editor.isActive('bulletList') ? 'bg-gray-200' : 'hover:bg-gray-100'}`} title="Seznam s odrážkami">
                        <FontAwesomeIcon icon={faListUl} />
                    </button>
                    <button type="button" onClick={() => editor.chain().focus().toggleOrderedList().run()} className={`toggle-button w-8 h-8 rounded-md ${editor.isActive('orderedList') ? 'bg-gray-200' : 'hover:bg-gray-100'}`} title="Číslovaný seznam">
                        <FontAwesomeIcon icon={faListOl} />
                    </button>
                    <button type="button" onClick={() => editor.chain().focus().toggleBlockquote().run()} className={`toggle-button w-8 h-8 rounded-md ${editor.isActive('blockquote') ? 'bg-gray-200' : 'hover:bg-gray-100'}`} title="Citace">
                        <FontAwesomeIcon icon={faQuoteLeft} />
                    </button>
                </div>

                {/* --- ZMĚNA V SEKCI PRO VKLÁDÁNÍ --- */}
                <div className="flex items-center space-x-1">
                    <button type="button" onClick={setLink} className={`toggle-button w-8 h-8 rounded-md ${editor.isActive('link') ? 'bg-gray-200' : 'hover:bg-gray-100'}`} title="Vložit odkaz">
                        <FontAwesomeIcon icon={faLink} />
                    </button>

                    <button
                        type="button"
                        onClick={() => fileInputRef.current?.click()}
                        className="toggle-button w-8 h-8 rounded-md hover:bg-gray-100 flex justify-center items-center"
                        title="Vložit obrázek"
                        disabled={isUploading} // Zabráníme dalšímu kliknutí během nahrávání
                    >
                        {isUploading ? (
                            <span className="loader" /> // Náš nový spinner
                        ) : (
                            <FontAwesomeIcon icon={faImage} /> // Původní ikona
                        )}
                    </button>

                    <button type="button" onClick={() => editor.chain().focus().setHorizontalRule().run()} className="toggle-button w-8 h-8 rounded-md hover:bg-gray-100" title="Vodorovná čára">
                        <FontAwesomeIcon icon={faMinus} />
                    </button>
                </div>
            </div>

            {/* --- NOVÝ PRVEK: Skrytý input pro výběr souboru --- */}
            <input
                type="file"
                ref={fileInputRef}
                onChange={handleImageUpload}
                disabled={isUploading}
                className="hidden"
                accept="image/*"
            />
        </div>
    );
};

ArticleEditorToolbar.propTypes = {
    editor: PropTypes.object,
    blockTheme: PropTypes.string
};

export default ArticleEditorToolbar;