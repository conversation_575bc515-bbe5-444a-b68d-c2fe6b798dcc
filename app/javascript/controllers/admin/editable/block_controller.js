import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
    static values = { name: String, container: String, themeValue: String, alignment: String, mediaAlignment: String, mediaContainer: String };

    // border color set by containerName

    connect() {
        const containerElement = document.getElementById(this.containerValue);

        if (this.nameValue === 'content-layer') {
            containerElement.classList.add('border-3', 'border-purple-600');
            containerElement.dataset.borderColor = 'purple';
        } else if (this.nameValue === 'inner-container-layer') {
            containerElement.classList.add('border-3', 'border-orange-600');
            containerElement.dataset.borderColor = 'orange';
        } else if (this.nameValue === 'outer-container-layer') {
            containerElement.classList.add('border-3', 'border-green-600');
            containerElement.dataset.borderColor = 'green';
        }  else if (this.nameValue === 'media-layer') {
            containerElement.classList.add('border-3', 'border-yellow-600');
            containerElement.dataset.borderColor = 'yellow';
        }
    }

    onChangeTheme(event) {
        const { value: theme } = event.target;
        const blockElement = document.getElementById(this.containerValue);

        if (blockElement) {
          blockElement.dataset.theme = theme;
          this.themeValue = theme;
        } else {
            console.error(`Block with ID 'block-${this.containerValue}' not found.`);
        }
    }

    onChangeBackgroundColor(event) {
        const { value: backgroundColor } = event.target;

        const blockElement = document.getElementById(this.containerValue);
        const overlayBlockElement = document.getElementById(`${this.containerValue}-overlay`);

        if (blockElement) {
            blockElement.dataset.theme = backgroundColor;
        }

        if (overlayBlockElement) {
            overlayBlockElement.dataset.theme = backgroundColor;
        }

        //this.outerContainerLayerValue.back = backgroundColor;
    }

    onChangeAlignment(event) {
        const { value: alignment } = event.target;
        const blockElement = document.getElementById(this.containerValue);

        if (blockElement) {
            blockElement.classList.remove(this.alignmentValue);
            blockElement.classList.add(alignment);
            this.alignmentValue = alignment;
        } else {
           console.error(`Block ${this.containerValue} not found.`);
        }
    }

    onChangeMediaAlignment(event) {
        const {value: mediaAlignment} = event.target;
        const blockElement = document.getElementById(this.mediaContainerValue);

        if (blockElement) {
            blockElement.classList.remove(this.mediaAlignmentValue);
            blockElement.classList.add(mediaAlignment);
            this.mediaAlignmentValue = mediaAlignment;
        } else {
            console.error(`Block ${this.containerValue} not found.`);
        }
    }
}