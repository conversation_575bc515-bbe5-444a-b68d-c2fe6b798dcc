import {Controller} from "@hotwired/stimulus";

export default class extends Controller {
    static targets = ["previewButton", "iframeContainer", "mainContent", "toggleButton", "toggleText"];

    connect() {
        this.isMobilePreview = false;
        this.iframe = null;
    }

    togglePreview() {
        this.isMobilePreview = !this.isMobilePreview;

        if (this.isMobilePreview) {
            this.showMobilePreview();
        } else {
            this.hideMobilePreview();
        }
    }

    showMobilePreview() {
        // Skrytí hlavního obsahu
        this.mainContentTarget.style.display = "none";

        // Aktualizace tlačítek
        this.updateButtonStates(true);

        // Vytvoření iframe pro mobilní náhled
        this.createIframe();
    }

    hideMobilePreview() {
        // Zobrazení hlavního obsahu
        this.mainContentTarget.style.display = "block";

        // Aktualizace tlačítek
        this.updateButtonStates(false);

        // Odstranění iframe
        this.removeIframe();
    }

    createIframe() {
        this.iframe = document.createElement('iframe');
        this.iframe.src = window.location.href; // Načte aktuální stránku
        this.iframe.style.width = "393px";  // Šířka mobilního zařízení
        this.iframe.style.height = "100vh"; // Výška mobilního zařízení
        this.iframe.style.border = "none";
        this.iframe.style.position = "absolute";
        this.iframe.style.top = "0";
        this.iframe.style.left = "50%";
        this.iframe.style.transform = "translateX(-50%)";

        // Nastavení události pro načtení iframe
        this.iframe.onload = () => {

            // Přidání meta tagu do iframe dokumentu
            this.iframe.contentWindow.document.head.appendChild(meta);
        };

        this.iframeContainerTarget.appendChild(this.iframe);
    }

    removeIframe() {
        if (this.iframe) {
            this.iframe.remove();
            this.iframe = null;
        }
    }

    updateButtonStates(isMobilePreview) {
        // Aktualizace desktop tlačítka
        if (this.hasToggleButtonTarget) {
            if (isMobilePreview) {
                this.toggleButtonTarget.classList.remove("bg-gray-100", "text-gray-700", "hover:bg-gray-200");
                this.toggleButtonTarget.classList.add("bg-black", "text-white", "hover:bg-gray-800");
            } else {
                this.toggleButtonTarget.classList.remove("bg-black", "text-white", "hover:bg-gray-800");
                this.toggleButtonTarget.classList.add("bg-gray-100", "text-gray-700", "hover:bg-gray-200");
            }
        }

        // Aktualizace textu
        const newText = isMobilePreview ? "Zavřít náhled" : "Mobilní náhled";

        if (this.hasToggleTextTarget) {
            this.toggleTextTarget.textContent = newText;
        }
    }
}
