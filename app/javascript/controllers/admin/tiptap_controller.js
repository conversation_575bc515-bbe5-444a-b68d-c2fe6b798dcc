import { Controller } from "@hotwired/stimulus"
import React from 'react';
import { createRoot } from 'react-dom/client';

// NOVÉ: Importujeme obě komponenty
import TiptapEditor from '../../components/TiptapEditor.jsx';
import ArticleEditor from '../../components/ArticleEditor.jsx'; // Ujistěte se, že cesta je správná

// NOVÉ: Mapa pro výběr komponenty
const EDITOR_COMPONENTS = {
    bubble: TiptapEditor,
    article: ArticleEditor,
};

export default class extends Controller {
    connect() {
        const container = this.element;

        if (container.dataset.reactBooted) {
            return;
        }
        container.dataset.reactBooted = "true";

        const {
            initialContent,
            htmlInputId,
            jsonInputId,
            previewElementId,
            editorClassName,
            blockTheme,
            editorType // NOVÉ: Načteme i typ editoru
        } = container.dataset;

        const htmlInput = document.getElementById(htmlInputId);
        const jsonInput = document.getElementById(jsonInputId);
        const previewEl = document.getElementById(previewElementId);

        const parsedInitialContent = JSON.parse(initialContent || 'null');
        const root = createRoot(container);

        console.log(jsonInput.value)

        const EditorComponent = EDITOR_COMPONENTS[editorType] || ArticleEditor;

        root.render(
            <EditorComponent
                initialContent={parsedInitialContent}
                editorClassName={editorClassName}
                blockTheme={blockTheme}
                onUpdateHTML={(html) => {
                    if (htmlInput) {
                        htmlInput.value = html;
                    }
                    if (previewEl) {
                        previewEl.innerHTML = html;
                    }
                }}
                onUpdateJSON={(json) => {
                    if (jsonInput) {
                        jsonInput.value = JSON.stringify(json);
                    }
                }}
            />
        );
    }
}