import { Controller } from "@hotwired/stimulus";
import Sortable from "sortablejs";
import { patch } from "@rails/request.js"

export default class extends Controller {
  static targets = ["sortable", "item"];

  static values = {
    responseKind: { type: String, default: "turbo-stream" }
  }

  initialize() {
    this.onUpdate = this.onUpdate.bind(this)
  }

  connect() {
    console.log(Sortable.active)
    this.endpointUrl = this.element.dataset.endpointUrl;

    if (Sortable.active) {
      this.sortable = Sortable.get(this.element)
    } else {
      this.sortable = Sortable.create(this.element, {
        group: false,
        fallbackOnBody: true,
        sort: true,
        animation: 150,
        handle: ".handle",
        forceFallback: false,
        filter: '.filtered',
        onUpdate: this.onUpdate,
        ghostClass: "sortable-ghost",
        swapThreshold: 0.65,
      });
    }
  }

  disconnect() {
    if (!Sortable.active) {
     this.sortable.destroy()
    }
    this.sortable = undefined
  }

  async onUpdate({ item, newIndex }) {
    if (!item.dataset.sortableUpdateUrl) return

    const param = "position"

    const data = new FormData()
    data.append(param, newIndex + 1)

    return await patch(item.dataset.sortableUpdateUrl, { body: data, responseKind: this.responseKindValue })
  }
}
