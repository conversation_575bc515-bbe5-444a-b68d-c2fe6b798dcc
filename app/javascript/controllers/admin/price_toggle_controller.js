import { Controller } from "@hotwired/stimulus"

// Vylepšená verze controlleru, která používá Stimulus Values
// a je lépe přizpůsobena pro práci s checkboxy.
export default class extends Controller {
    static targets = ["numericWrapper", "textualWrapper"]
    static values = {
        // Definujeme 'isTextual' jako boolean hodnotu, kterou budeme ovládat
        isTextual: Boolean
    }

    connect() {
        // Při prvním připojení controlleru nastavíme správnou viditelnost
        this.updateVisibility()
    }

    toggle(event) {
        // Aktualizujeme stav přímo z vlastnosti 'checked' checkboxu
        this.isTextualValue = event.target.checked
    }

    // Tato metoda se zavolá automaticky pokaždé, když se změní `isTextualValue`
    isTextualValueChanged() {
        this.updateVisibility()
    }

    updateVisibility() {
        const isTextual = this.isTextualValue

        // Přepneme viditelnost wrapperů na základě aktuální hodnoty
        this.numericWrapperTarget.classList.toggle('hidden', isTextual)
        this.textualWrapperTarget.classList.toggle('hidden', !isTextual)

        // Logika pro vyčištění hodnot skrytých polí, aby se neodesílala zbytečná data.
        // Důležité pro čistotu dat v databázi.
        if (this.hasNumericWrapperTarget && isTextual) {
            this.numericWrapperTarget.querySelectorAll('input').forEach(input => input.value = '')
        }

        if (this.hasTextualWrapperTarget && !isTextual) {
            this.textualWrapperTarget.querySelector('input').value = ''
        }
    }
}
