import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
    static targets = ["testInfo", "customDomain"]
    static values = {
        useCustom: Boolean
    }

    connect() {
        this.updateVisibility()
    }

    toggle(event) {
        this.useCustomValue = event.target.checked
    }

    useCustomValueChanged() {
        this.updateVisibility()
    }

    updateVisibility() {
        // Kontrola existence targets před použit<PERSON>m
        if (this.hasTestInfoTarget && this.hasCustomDomainTarget) {
            if (this.useCustomValue) {
                this.testInfoTarget.classList.add("hidden")
                this.customDomainTarget.classList.remove("hidden")
            } else {
                this.testInfoTarget.classList.remove("hidden")
                this.customDomainTarget.classList.add("hidden")
            }
        }
    }
}
