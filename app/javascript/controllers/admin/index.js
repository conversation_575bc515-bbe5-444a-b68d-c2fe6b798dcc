import { application } from "../application"

import Hw<PERSON>omboboxController from "@josefarias/hotwire_combobox"

import Notification from '@stimulus-components/notification'
import { Tabs } from "tailwindcss-stimulus-components"
import RailsNestedForm from '@stimulus-components/rails-nested-form'
import Sortable from "./sortable_controller"
import CreateMenuController from './create_menu_controller'
import ToggleCheckboxController from './toggle_checkbox_controller'
import OpeningHoursController from './opening_hours_controller'
import DropdownController from './dropdown_controller'
import SliderController from './slider_controller'
import SplideController from '../splide_controller'
import TippyController  from "./tippy_controller";
import UploadsController from "./uploads_controller";
import MobilePreviewController from "./editable/mobile_preview_controller";
import TiptapController from "./tiptap_controller";
import RevealController from '@stimulus-components/reveal'
import ImagePreviewController from '../image_preview_controller'
import <PERSON><PERSON><PERSON>roller from "./editable/form_controller";
import PriceToggleController from "./price_toggle_controller"
import DomainToggleController from "./domain_toggle_controller"
import ServiceToggleController from "./service_toggle_controller"

// Editable:

import HeadingController from "./editable/heading_controller";
import ParagraphController from "./editable/paragraph_controller";
import ButtonsController from "./editable/buttons_controller";
import BlockController from "./editable/block_controller";
import IconPickerController from "./editable/icon_picker_controller";

application.register('notification', Notification)
application.register('tabs', Tabs)
application.register('dropdown', DropdownController)
application.register('nested-form', RailsNestedForm)
application.register('sortable', Sortable)
application.register('create-menu', CreateMenuController)
application.register('toggle-checkbox', ToggleCheckboxController)
application.register('opening-hours', OpeningHoursController)
application.register("hw-combobox", HwComboboxController)
application.register("slider", SliderController)
application.register("heading", HeadingController)
application.register("paragraph", ParagraphController)
application.register("buttons", ButtonsController)
application.register("block", BlockController)
application.register('splide', SplideController)
application.register('tippy', TippyController)
application.register('uploads', UploadsController)
application.register('icon-picker', IconPickerController)
application.register('mobile-preview', MobilePreviewController)
application.register('tiptap', TiptapController)
application.register('reveal', RevealController)
application.register('image-preview', ImagePreviewController)
application.register('form', FormController)
application.register('price-toggle', PriceToggleController)
application.register('domain-toggle', DomainToggleController)
application.register('service-toggle', ServiceToggleController)