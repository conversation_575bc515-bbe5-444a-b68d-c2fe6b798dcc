import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["input", "image", "placeholder", "imageWrapper", "removeButton"]

  // 3) <PERSON>kce po výběru souboru v dialogovém okně
  preview() {
    const file = this.inputTarget.files[0]

    if (file) {
      const reader = new FileReader()

      reader.onload = (e) => {
        // Nastavíme zdroj obrázku na data z vybraného souboru
        this.imageTarget.src = e.target.result
        this.showImage()
      }

      reader.readAsDataURL(file)
    }
  }

  // 2) Otevře dialogové okno pro výběr souboru
  openFileDialog() {
    this.inputTarget.click()
  }

  // 4) "Zruší výběr" nového obrázku (ješt<PERSON> před uložením)
  clearSelection(event) {
    // Zabráníme případnému submitu formuláře, pokud by byl button typu "submit"
    event.preventDefault()
    // Vyčistíme hodnotu file inputu
    this.inputTarget.value = ''
    this.showPlaceholder()
  }

  // Pomocná metoda pro zobrazení obrázku a skrytí placeholderu
  showImage() {
    this.imageWrapperTarget.classList.remove('hidden')
    this.placeholderTarget.classList.add('hidden')
    // Zobrazíme tlačítko pro zrušení výběru (pro nový obrázek)
    this.removeButtonTarget.classList.remove('hidden')
  }

  // Pomocná metoda pro zobrazení placeholderu a skrytí obrázku
  // Tuto metodu voláme jak při zrušení výběru (clearSelection), tak
  // při smazání již uloženého obrázku (data-action na link_to).
  showPlaceholder() {
    this.imageWrapperTarget.classList.add('hidden')
    this.placeholderTarget.classList.remove('hidden')
    // Skryjeme tlačítko pro zrušení výběru, protože teď žádný není
    this.removeButtonTarget.classList.add('hidden')
  }
}