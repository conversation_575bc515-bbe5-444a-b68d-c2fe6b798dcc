# == Schema Information
#
# Table name: pricing_items
#
#  id                    :bigint           not null, primary key
#  content_json          :jsonb
#  detail_layout_variant :integer          default("horizontal"), not null
#  duration              :integer
#  is_price_textual      :boolean
#  position              :integer
#  price                 :decimal(10, 2)
#  price_eur             :decimal(10, 2)
#  price_textual         :string
#  translations          :jsonb            not null
#  created_at            :datetime         not null
#  updated_at            :datetime         not null
#  pricing_section_id    :bigint           not null
#  website_id            :integer
#
# Indexes
#
#  index_pricing_items_on_pricing_section_id               (pricing_section_id)
#  index_pricing_items_on_pricing_section_id_and_position  (pricing_section_id,position) UNIQUE
#  index_pricing_items_on_website_id                       (website_id)
#
# Foreign Keys
#
#  fk_rails_...  (pricing_section_id => pricing_sections.id)
#
class PricingItem < ApplicationRecord
  extend Mobility

  translates :name, :description, :slug, :content, backend: :container
  translates :content_json, backend: :jsonb

  belongs_to :pricing_section
  acts_as_tenant :website

  has_one :pricing, through: :pricing_section

  positioned on: :pricing_section_id

  before_save :generate_slugs_if_needed

  has_one_attached :image do |attachable|
    attachable.variant :thumb, resize_to_fill: [40, 40]
    attachable.variant :preview, resize_to_limit: [200, 200], saver: { quality: 100 }
    attachable.variant :vertical, resize_to_limit: [500, 500], saver: { quality: 100 }
    attachable.variant :horizontal, resize_to_fill: [250, 250], saver: { quality: 100 }
  end

  validates :name_cs, presence: true

  EXCHANGE_RATE = 25

  enum :detail_layout_variant, [ :horizontal, :vertical ]

  def price_eur
    if read_attribute(:price_eur).present?
      read_attribute(:price_eur)
    else
      price ? pretty_price(price, EXCHANGE_RATE) : nil
    end
  end

  def content_present?
    true
  end

  private

  def generate_slugs_if_needed
    website.available_locales.each do |locale|
      Mobility.with_locale(locale.to_sym) do
        if name_changed? && name(locale: locale).present?
          base_slug = name(locale: locale).parameterize
          unique_slug = resolve_slug_uniqueness(base_slug, locale)
          public_send("slug_#{locale}=", unique_slug)
        end
      end
    end
  end

  def resolve_slug_uniqueness(slug, locale)
    # Vytvoříme "scope" - základní dotaz na všechny ostatní položky
    # .where.not(id: self.id) je ZÁSADNÍ, abychom při editaci neporovnávali záznam sám se sebou.
    scope = self.class.where.not(id: self.id)

    # Připravíme si kandidáta na finální slug
    candidate = slug
    counter = 1

    # Smyčka: Dokud v databázi existuje jiný záznam s naším kandidátem na slug...
    while scope.where("slug -> :locale = :slug", locale: locale, slug: candidate).exists?
      # ...zvyšujeme počítadlo a vytváříme nového kandidáta s příponou (např. "muj-slug-2")
      counter += 1
      candidate = "#{slug}-#{counter}"
    end

    # Vrátíme prvního kandidáta, který v databázi neexistuje
    candidate
  end

  def should_generate_new_friendly_id?
    name_changed? || super
  end

  def pretty_price(czk_price, exchange_rate = EXCHANGE_RATE)
    eur_price = czk_price / exchange_rate
    if eur_price < 1
      0.99
    elsif eur_price < 10
      if eur_price % 1 < 0.5
        (eur_price.floor + 0.49).round(2)
      else
        (eur_price.floor + 0.99).round(2)
      end
    else
      if eur_price % 1 < 0.5
        (eur_price.floor + 0.49).round(2)
      else
        (eur_price.floor + 0.99).round(2)
      end
    end
  end
end
