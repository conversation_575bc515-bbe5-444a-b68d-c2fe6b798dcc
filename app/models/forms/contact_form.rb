# == Schema Information
#
# Table name: forms
#
#  id           :bigint           not null, primary key
#  booking_type :integer
#  description  :text
#  name         :string
#  type         :string
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  user_id      :bigint           not null
#  website_id   :bigint           not null
#
# Indexes
#
#  index_forms_on_user_id     (user_id)
#  index_forms_on_website_id  (website_id)
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#  fk_rails_...  (website_id => websites.id)
#
class ContactForm < Form
  after_create :create_default_fields

  private

  def create_default_fields
    transaction do
      form_fields.create!(
        field_type: :email,
        label: 'E-mail',
        required: true,
        position: 1,
        custom: true
      )

      form_fields.create!(
        field_type: :textarea,
        label: 'Zpráva',
        required: true,
        position: 2,
        custom: true
      )
    end
  end
end
