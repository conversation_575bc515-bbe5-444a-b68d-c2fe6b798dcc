# app/models/layout.rb
# == Schema Information
#
# Table name: layouts
#
#  id                   :bigint           not null, primary key
#  name                 :string
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  main_footer_block_id :bigint
#  main_header_block_id :bigint
#  website_id           :bigint           not null
#
# Indexes
#
#  index_layouts_on_main_footer_block_id  (main_footer_block_id)
#  index_layouts_on_main_header_block_id  (main_header_block_id)
#  index_layouts_on_website_id            (website_id)
#
# Foreign Keys
#
#  fk_rails_...  (main_footer_block_id => blocks.id)
#  fk_rails_...  (main_header_block_id => blocks.id)
#  fk_rails_...  (website_id => websites.id)
#
class Layout < ApplicationRecord
  acts_as_tenant(:website)

  belongs_to :main_header_block, class_name: 'Block', optional: true
  belongs_to :main_footer_block, class_name: 'Block', optional: true

  has_many :layout_blocks, -> { order(position: :asc) }, dependent: :destroy

  has_many :global_header_blocks,
           -> { where(layout_blocks: { location: 'header' }).order('layout_blocks.position ASC') },
           through: :layout_blocks,
           source: :block

  has_many :global_footer_blocks,
           -> { where(layout_blocks: { location: 'footer' }).order('layout_blocks.position ASC') },
           through: :layout_blocks,
           source: :block

  validates :name, presence: true

  def title
    name
  end
end
