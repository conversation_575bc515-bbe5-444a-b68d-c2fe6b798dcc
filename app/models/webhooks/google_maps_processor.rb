require "uri"

class Webhooks::GoogleMapsProcessor
  def self.process(webhook)
    dataset = fetch_dataset(webhook.payload["resource"]["defaultDatasetId"])
    process_items(dataset)
  end

  def self.fetch_dataset(dataset_id)
    client = Integrations::Apify::Client.new("**********************************************")
    client.get_dataset_items(dataset_id)
  end

  def self.process_items(items)
    items.each do |item|
      place_id = item["placeId"]
      service = Integrations::GoogleMaps.where("options->>'place_id' = ?", place_id).take
      website = service.website

      if items["openingHours"]
        website.update(opening_hours: parse_opening_hours(items["openingHours"]))
      end

      if service.import_reviews
        process_reviews(website, item["reviews"])
      end
    end
  end

  def self.parse_opening_hours(hours)
    return if hours.blank?

    day_map = {
      "pondělí" => "Mo",
      "úterý" => "Tu",
      "středa" => "We",
      "čtvrtek" => "Th",
      "pátek" => "Fr",
      "sobota" => "Sa",
      "neděle" => "Su"
    }

     hours.map do |entry|
      day = day_map[entry["day"]]
      open, close = entry["hours"].split(" to ")
      { day: day, open: open, close: close }
    end
  end

  def self.process_reviews(website, reviews)
    reviews.each do |review|
      next if review["text"].blank? || review["name"].blank?

      record = website.reviews.find_or_create_by(origin_id: review["reviewId"]) do |r|
        r.name = review["name"]
        r.content = review["text"]
        r.published_at = review["publishedAtDate"]
        r.origin_name = review["reviewOrigin"]
        r.origin_url = review["reviewUrl"]
        r.rating = review["stars"]
      end

      if review["reviewerPhotoUrl"]
        record.avatar.attach(io: URI.open(review["reviewerPhotoUrl"]), filename: "#{record.origin_id}.jpg")
      end
    end
  end
end
