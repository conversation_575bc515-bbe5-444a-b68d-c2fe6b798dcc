require "uri"

class Webhooks::InstagramProcessor
  def self.process(webhook)
    dataset = fetch_dataset(webhook.payload["resource"]["defaultDatasetId"])
    process_items(dataset)
  end

  private

  def self.fetch_dataset(dataset_id)
    client = Integrations::Apify::Client.new("**********************************************")
    client.get_dataset_items(dataset_id)
  end

  def self.find_website_by_instagram_url(instagram_url)
    # Normalize the URL to handle different formats
    normalized_url = normalize_instagram_url(instagram_url)

    Service.find_by("options ->> 'instagram_url' = ?", normalized_url)
  end

  def self.normalize_instagram_url(url)
    # Remove trailing slash and ensure consistent format
    url.to_s.chomp('/')
  end

  def self.process_items(items)
    # Get Gallery media type for Instagram posts
    gallery_media_type = MediaType.find_by(slug: "gallery")

    items.each do |item|
      website = find_website_by_instagram_url(item["inputUrl"])
      next unless website

      # Find or create Instagram MediaCollection for this website
      instagram_collection = MediaCollection.find_by(website_id: website.website_id, source: 'instagram')

      unless instagram_collection
        instagram_collection = MediaCollection.create!(
          website_id: website.website_id,
          name: "Instagram Posts",
          source: "instagram",
          media_type_id: gallery_media_type.id
        )
      end

      # Create or update Media entity
      media_item = instagram_collection.media.find_or_create_by(unique_id: item["id"]) do |media|
        # Set basic attributes for new media items
        media.website_id = website.website_id
        media.published_at = item["timestamp"]
        media.origin = "Instagram"
        media.media_type = gallery_media_type if gallery_media_type
      end

      # Update media content (this works for both new and existing records)
      update_media_content(media_item, item)
      media_item.save! if media_item.changed?

      # Attach image
      attach_image(media_item, item)
    end
  end

  def self.update_media_content(media_item, item)
    # Ensure the media item is saved first so it has an ID
    media_item.save! if media_item.new_record?

    # Now we can safely create or update the content
    content = media_item.contents.find_or_initialize_by(locale: :cs)
    content.assign_attributes(
      title: item["caption"]&.truncate(100) || "Instagram Post",
      caption: item["caption"],
      custom_fields: {
        media_type: item["type"],
        media_url: item["displayUrl"],
        url: item["url"],
        caption: item["caption"]
      }
    )
    content.save!
  end

  def self.attach_image(media_item, item)
    return if media_item.image.attached?

    begin
      media_item.image.attach(
        io: URI.open(item["displayUrl"]),
        filename: "#{item['id']}.jpg"
      )
    rescue => e
      Rails.logger.error "Failed to attach Instagram image for post #{item['id']}: #{e.message}"
    end
  end
end
