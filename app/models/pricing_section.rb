# == Schema Information
#
# Table name: pricing_sections
#
#  id         :bigint           not null, primary key
#  name       :hstore           not null
#  position   :integer
#  valid_date :date
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  pricing_id :bigint           not null
#
# Indexes
#
#  index_pricing_sections_on_pricing_id  (pricing_id)
#
# Foreign Keys
#
#  fk_rails_...  (pricing_id => pricing.id)
#

class PricingSection < ApplicationRecord
  extend Mobility

  translates :name

  belongs_to :pricing
  acts_as_list scope: :pricing

  validates :name, presence: true

  has_many :pricing_items, -> { order(position: :asc) }, dependent: :destroy
  accepts_nested_attributes_for :pricing_items, reject_if: :all_blank, allow_destroy: true
end
