# == Schema Information
#
# Table name: form_fields
#
#  id           :bigint           not null, primary key
#  custom       :boolean          default(FALSE), not null
#  field_type   :string           not null
#  options      :jsonb            not null
#  position     :integer          default(0), not null
#  required     :boolean          default(FALSE), not null
#  translations :jsonb            not null
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  form_id      :bigint           not null
#
# Indexes
#
#  index_form_fields_on_form_id               (form_id)
#  index_form_fields_on_form_id_and_position  (form_id,position)
#  index_form_fields_on_translations          (translations) USING gin
#
# Foreign Keys
#
#  fk_rails_...  (form_id => forms.id)
#
class FormField < ApplicationRecord
  extend Mobility

  belongs_to :form

  enum :field_type, {
    text: 'text',
    email: 'email',
    textarea: 'textarea',
    #   select: 'select',
    date: 'date',
    checkbox: 'checkbox',
    radio: 'radio'
  }

  translates :label, :submit_text, backend: :container

  validates :field_type, presence: true

  positioned on: %i[form_id]

  before_destroy :prevent_custom_field_deletion

  private

  def prevent_custom_field_deletion
    if custom?
      errors.add(:base, 'Toto systémové pole nelze smazat.')
      throw :abort
    end
  end
end
