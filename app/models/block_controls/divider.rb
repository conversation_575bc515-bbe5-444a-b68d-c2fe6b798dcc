# == Schema Information
#
# Table name: block_controls
#
#  id           :bigint           not null, primary key
#  classes      :jsonb
#  container    :string
#  content      :jsonb
#  options      :jsonb
#  position     :integer          default(0)
#  styles       :jsonb
#  translations :jsonb            not null
#  type         :string
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  block_id     :bigint
#
# Indexes
#
#  index_block_controls_on_block_id  (block_id)
#
# Foreign Keys
#
#  fk_rails_...  (block_id => blocks.id)
#
class BlockControls::Divider < BlockControl
  store_accessor :options, :width, :height

  def self.permitted_attributes
    %i[id width height]
  end
end
