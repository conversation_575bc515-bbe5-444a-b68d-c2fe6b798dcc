# == Schema Information
#
# Table name: forms
#
#  id           :bigint           not null, primary key
#  booking_type :integer
#  description  :text
#  name         :string
#  type         :string
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  user_id      :bigint           not null
#  website_id   :bigint           not null
#
# Indexes
#
#  index_forms_on_user_id     (user_id)
#  index_forms_on_website_id  (website_id)
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#  fk_rails_...  (website_id => websites.id)
#
class Form < ApplicationRecord
  belongs_to :user

  has_many :form_fields, -> { order(position: :asc) }, dependent: :delete_all
  accepts_nested_attributes_for :form_fields, allow_destroy: true

  has_many :form_submissions, dependent: :nullify

  acts_as_tenant(:website)

  validates :name, :type, presence: true

  def total_submissions_count
    form_submissions.size
  end

  # Vrací datum posledního odes<PERSON>ání.
  def last_submission_at
    # .order.first je efektivní, proto<PERSON>e datab<PERSON>ze najde první záznam podle indexu.
    form_submissions.order(created_at: :desc).first&.created_at
  end

  # Vrá<PERSON><PERSON> několik posledních odeslání pro rychlý náhled.
  def recent_submissions(limit = 3)
    form_submissions.order(created_at: :desc).limit(limit)
  end
end
