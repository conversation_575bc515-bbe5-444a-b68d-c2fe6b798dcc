# frozen_string_literal: true

# == Schema Information
#
# Table name: block_containers
#
#  id                         :bigint           not null, primary key
#  alignment                  :string
#  background_overlay_opacity :string
#  container                  :string
#  gap                        :string
#  gap_y                      :string
#  media_alignment            :string
#  padding_x                  :string
#  padding_y                  :string
#  theme                      :string
#  type                       :string
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#  block_id                   :bigint           not null
#
# Indexes
#
#  index_block_containers_on_block_id  (block_id)
#
# Foreign Keys
#
#  fk_rails_...  (block_id => blocks.id)
#
class OuterBlockContainer < BlockContainer
  THEMES = %w[auto soft light neutral primary white black].freeze
  CONTAINER_SIZES = %w[max-w-xl max-w-2xl max-w-3xl max-w-4xl max-w-5xl max-w-6xl w-full].freeze
  PADDINGS_Y = %w[sm:py-0 sm:py-0.5 sm:py-1 sm:py-2 sm:py-4 sm:py-6 sm:py-8 sm:py-10 sm:py-12 sm:py-16 sm:py-20 sm:py-24 sm:py-32 sm:py-64].freeze
  OVERLAY_OPACITIES = %w[opacity-0 opacity-10 opacity-20 opacity-30 opacity-40 opacity-50 opacity-60 opacity-70 opacity-80 opacity-90 opacity-100].freeze

  DEFAULTS = {
    theme: 'soft',
    container: 'w-full',
    padding_y: 'sm:py-16',
    background_overlay_opacity: 'opacity-0'
  }.freeze

  validates :theme, inclusion: { in: THEMES, message: "Téma '%{value}' není povoleno" }, presence: true
  validates :container, inclusion: { in: CONTAINER_SIZES, message: "Velikost '%{value}' není povolena" }, presence: true
  validates :padding_y, inclusion: { in: PADDINGS_Y, message: "Odsazení Y '%{value}' není povoleno" }, presence: true
  validates :background_overlay_opacity, inclusion: { in: OVERLAY_OPACITIES, message: "Překrytí '%{value}' není povoleno" }, presence: true
end