# frozen_string_literal: true

# == Schema Information
#
# Table name: block_containers
#
#  id                         :bigint           not null, primary key
#  alignment                  :string
#  background_overlay_opacity :string
#  container                  :string
#  gap                        :string
#  gap_y                      :string
#  media_alignment            :string
#  padding_x                  :string
#  padding_y                  :string
#  theme                      :string
#  type                       :string
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#  block_id                   :bigint           not null
#
# Indexes
#
#  index_block_containers_on_block_id  (block_id)
#
# Foreign Keys
#
#  fk_rails_...  (block_id => blocks.id)
#
# frozen_string_literal: true

# == Schema Information
#
# Table name: block_containers
#
#  id                         :bigint           not null, primary key
#  alignment                  :string
#  background_overlay_opacity :string
#  container                  :string
#  gap                        :string
#  gap_y                      :string
#  media_alignment            :string
#  padding_x                  :string
#  padding_y                  :string
#  theme                      :string
#  type                       :string
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#  block_id                   :bigint           not null
#
# Indexes
#
#  index_block_containers_on_block_id  (block_id)
#
# Foreign Keys
#
#  fk_rails_...  (block_id => blocks.id)
#
class MediaBlockContainer < BlockContainer
  THEMES = %w[auto soft light neutral primary white black].freeze
  CONTAINER_SIZES = %w[container-half container-two-thirds container-three-quarters container-wide container-very-wide w-full].freeze
  PADDINGS_Y = %w[sm:py-0 sm:py-0.5 sm:py-1 sm:py-2 sm:py-4 sm:py-6 sm:py-8 sm:py-10 sm:py-12 sm:py-16 sm:py-20 sm:py-24 sm:py-32 sm:py-64].freeze

  DEFAULTS = {
    theme: 'auto',
    container: 'w-full',
    padding_y: 'sm:py-0'
  }.freeze

  validates :theme, inclusion: { in: THEMES, message: "Téma '%{value}' není povoleno" }, presence: true
  validates :container, inclusion: { in: CONTAINER_SIZES, message: "Velikost '%{value}' není povolena" }, presence: true
  validates :padding_y, inclusion: { in: PADDINGS_Y, message: "Odsazení Y '%{value}' není povoleno" }, presence: true
end