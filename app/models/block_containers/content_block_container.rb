# frozen_string_literal: true

# == Schema Information
#
# Table name: block_containers
#
#  id                         :bigint           not null, primary key
#  alignment                  :string
#  background_overlay_opacity :string
#  container                  :string
#  gap                        :string
#  gap_y                      :string
#  media_alignment            :string
#  padding_x                  :string
#  padding_y                  :string
#  theme                      :string
#  type                       :string
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#  block_id                   :bigint           not null
#
# Indexes
#
#  index_block_containers_on_block_id  (block_id)
#
# Foreign Keys
#
#  fk_rails_...  (block_id => blocks.id)
#
class ContentBlockContainer < BlockContainer
  THEMES = %w[auto soft light neutral primary white black].freeze
  CONTAINER_SIZES = %w[container-half container-two-thirds container-three-quarters container-wide container-very-wide w-full].freeze
  PADDINGS_Y = %w[sm:py-0 sm:py-0.5 sm:py-1 sm:py-2 sm:py-4 sm:py-6 sm:py-8 sm:py-10 sm:py-12 sm:py-16 sm:py-20 sm:py-24 sm:py-32 sm:py-64].freeze
  PADDINGS_X = %w[sm:px-0 sm:px-0.5 sm:px-1 sm:px-2 sm:px-4 sm:px-6 sm:px-8 sm:px-10 sm:px-12 sm:px-16 sm:px-20 sm:px-24 sm:px-32 sm:px-64].freeze
  GAPS_Y = %w[gap-y-0 gap-y-0.5 gap-y-1 gap-y-1.5 gap-y-2 gap-y-2.5 gap-y-3 gap-y-3.5 gap-y-4 gap-y-4.5 gap-y-5 gap-y-5.5 gap-y-6 gap-y-8 gap-y-10 gap-y-12 gap-y-16 gap-y-20 gap-y-24].freeze
  ALIGNMENTS = %w[alignment-left alignment-center alignment-right].freeze

  DEFAULTS = {
    theme: 'auto',
    container: 'container-wide',
    padding_y: 'sm:py-16',
    padding_x: 'sm:px-8',
    gap_y: 'gap-y-4',
    alignment: 'alignment-center'
  }.freeze

  validates :theme, inclusion: { in: THEMES, message: "Téma '%{value}' není povoleno" }, presence: true
  validates :container, inclusion: { in: CONTAINER_SIZES, message: "Velikost '%{value}' není povolena" }, presence: true
  validates :padding_y, inclusion: { in: PADDINGS_Y, message: "Odsazení Y '%{value}' není povoleno" }, presence: true
  validates :padding_x, inclusion: { in: PADDINGS_X, message: "Odsazení X '%{value}' není povoleno" }, presence: true
  validates :gap_y, inclusion: { in: GAPS_Y, message: "Odsazení obsahu '%{value}' není povoleno" }, presence: true
  validates :alignment, inclusion: { in: ALIGNMENTS, message: "Zarovnání '%{value}' není povoleno" }, presence: true
end