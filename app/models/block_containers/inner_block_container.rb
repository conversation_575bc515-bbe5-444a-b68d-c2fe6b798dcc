# frozen_string_literal: true

# == Schema Information
#
# Table name: block_containers
#
#  id                         :bigint           not null, primary key
#  alignment                  :string
#  background_overlay_opacity :string
#  container                  :string
#  gap                        :string
#  gap_y                      :string
#  media_alignment            :string
#  padding_x                  :string
#  padding_y                  :string
#  theme                      :string
#  type                       :string
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#  block_id                   :bigint           not null
#
# Indexes
#
#  index_block_containers_on_block_id  (block_id)
#
# Foreign Keys
#
#  fk_rails_...  (block_id => blocks.id)
#
class InnerBlockContainer < BlockContainer
  THEMES = %w[auto soft light neutral primary white black].freeze
  CONTAINER_SIZES = %w[container-half container-two-thirds container-three-quarters container-wide container-very-wide w-full].freeze
  PADDINGS_Y = %w[sm:py-0 sm:py-0.5 sm:py-1 sm:py-2 sm:py-4 sm:py-6 sm:py-8 sm:py-10 sm:py-12 sm:py-16 sm:py-20 sm:py-24 sm:py-32 sm:py-64].freeze
  GAPS = %w[gap-0 gap-1 gap-2 gap-4 gap-6 gap-8 gap-10 gap-12 gap-16 gap-20 gap-24 gap-32].freeze
  MEDIA_ALIGNMENTS = %w[order-first order-last].freeze

  DEFAULTS = {
    theme: 'auto',
    container: 'w-full',
    padding_y: 'sm:py-0',
    gap: 'gap-8',
    media_alignment: 'order-last'
  }.freeze

  validates :theme, inclusion: { in: THEMES, message: "Téma '%{value}' není povoleno" }, presence: true
  validates :container, inclusion: { in: CONTAINER_SIZES, message: "Velikost '%{value}' není povolena" }, presence: true
  validates :padding_y, inclusion: { in: PADDINGS_Y, message: "Odsazení Y '%{value}' není povoleno" }, presence: true
  validates :gap, inclusion: { in: GAPS, message: "Odsazení obsahu '%{value}' není povoleno" }, presence: true
  validates :media_alignment, inclusion: { in: MEDIA_ALIGNMENTS, message: "Zarovnání média '%{value}' není povoleno" }, presence: true
end