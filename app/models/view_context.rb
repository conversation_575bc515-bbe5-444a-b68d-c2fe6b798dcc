class ViewContext
  attr_reader :pages_for_nav, :social_networks, :current_page, :locale, :type

  def initialize(page:, type: :frontend, locale: nil, available_locales: [])
    @current_page = page
    @type = type

    if locale
      @locale = available_locales.include?(locale) ? locale : :cs
    elsif page.present?
      @locale = page.locale
    end

    @pages_for_nav = Page.published.where(locale: @locale).order(:position).arrange
    @social_networks = Current.website.social_networks
  end

  def admin?
    @type == :admin
  end

  def frontend?
    @type == :frontend
  end
end