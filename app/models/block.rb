# == Schema Information
#
# Table name: blocks
#
#  id                  :bigint           not null, primary key
#  hidden_at           :datetime
#  media_options       :jsonb
#  name                :string
#  options             :jsonb
#  position            :integer
#  pricing_options     :jsonb
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  media_collection_id :bigint
#  media_type_id       :bigint
#  pricing_id          :bigint
#
# Indexes
#
#  index_blocks_on_media_collection_id  (media_collection_id)
#  index_blocks_on_media_type_id        (media_type_id)
#  index_blocks_on_pricing_id           (pricing_id)
#
# Foreign Keys
#
#  fk_rails_...  (media_collection_id => media_collections.id)
#  fk_rails_...  (media_collection_id => media_collections.id)
#  fk_rails_...  (media_type_id => media_types.id)
#  fk_rails_...  (pricing_id => pricing.id)
#
class Block < ApplicationRecord
  include ActionView::Helpers::SanitizeHelper
  include TailwindClassable

  # Associations
  belongs_to :media_collection, optional: true
  belongs_to :pricing, optional: true
  belongs_to :media_type, optional: true

  has_many :page_blocks, dependent: :destroy
  has_many :layout_blocks, dependent: :destroy
  has_many :pages, through: :page_blocks
  has_many :controls, -> { order(position: :asc) }, class_name: "BlockControl", dependent: :destroy
  has_many :media, through: :media_collection, class_name: "Media", source: :media

  CONTAINER_ORDER = %w[
    OuterBlockContainer
    InnerBlockContainer
    ContentBlockContainer
    MediaBlockContainer
  ].freeze

  # Sestavení SQL CASE příkazu pro řazení
  order_sql = "CASE type "
  CONTAINER_ORDER.each_with_index do |type, index|
    order_sql << "WHEN '#{type}' THEN #{index + 1} "
  end
  order_sql << "ELSE #{CONTAINER_ORDER.length + 1} END"

  has_many :block_containers, -> { order(Arel.sql(order_sql)) }, dependent: :destroy
  accepts_nested_attributes_for :block_containers, allow_destroy: true

  # Attachments
  has_one_attached :background_image do |attachable|
    attachable.variant :thumb, resize_to_fill: [120, 60]
    attachable.variant :preview, resize_to_limit: [ 1024, 768 ], saver: { quality: 100 }
  end

  has_one_attached :background_image_mobile

  # Scopes
  scope :visible, -> { where(hidden_at: nil) }

  # Store Accessor
  store_accessor :media_options, :posts_limit, :gap, :layout, :resize_image_options, :position, prefix: :media
  store_accessor :pricing_options, :pricing_type

  accepts_nested_attributes_for :controls, allow_destroy: true

  def hidden?
    hidden_at.present?
  end

  def block_name
    heading_text = controls.find { |c| c.type == "BlockControls::Heading" }&.text
    heading_text.present? ? strip_tags(heading_text) : "block-#{id}"
  end

  def to_combobox_display
    block_name
  end

  def has_media?
    media_type.present?
  end
  def has_pricing?
    pricing.present?
  end

  def theme
    content_layer.theme
  end

  def content_theme
    # content_layer.theme_class || inner_container_layer.theme_class || outer_container_layer.theme_class
  end

  def block_containers_by_type
    @block_containers_by_type ||= block_containers.index_by(&:type)
  end

  %w[OuterBlockContainer InnerBlockContainer MediaBlockContainer ContentBlockContainer].each do |type|
    define_method type.underscore.to_sym do
      block_containers.find_by(type: type)
    end
  end
end

