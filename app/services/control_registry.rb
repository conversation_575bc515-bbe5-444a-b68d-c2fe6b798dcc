module ControlRegistry
  CONTROL_CLASSES = {
    "BlockControls::Paragraph" => ParagraphControlData,
    "BlockControls::Heading"   => HeadingControlData,
    "BlockControls::Button"    => ButtonControlData,
    "BlockControls::Badge"     => nil,
    "BlockControls::Divider"   => DividerControlData,
  }.freeze

  def self.class_for(control_type)
    CONTROL_CLASSES.fetch(control_type) do
      raise "Unknown control type: #{control_type}"
    end
  end
end