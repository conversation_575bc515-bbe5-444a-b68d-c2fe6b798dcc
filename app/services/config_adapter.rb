class ConfigAdapter
  def self.for(source)
    case source
    when Block
      BlockConfigAdapter.new(source)
    when String, Symbol # Povolíme String i Symbol pro identifikátor
      YamlConfigAdapter.new(source.to_s) # Převedeme na String pro konzistenci
    else
      raise ArgumentError, "Unsupported source type: #{source.class}"
    end
  end

  # Metody rozhraní - do<PERSON>ny o ty, které BlockBuilder očekává
  def name; raise NotImplementedError, "#{self.class} has not implemented method '#{__method__}'"; end
  def controls_data; raise NotImplementedError, "#{self.class} has not implemented method '#{__method__}'"; end # Přejmenováno z controls
  def media_data; raise NotImplementedError, "#{self.class} has not implemented method '#{__method__}'"; end    # Přejmenováno z media
  def pricing_id; raise NotImplementedError, "#{self.class} has not implemented method '#{__method__}'"; end
  def pricing_options; raise NotImplementedError, "#{self.class} has not implemented method '#{__method__}'"; end
  def background_image_attachment; raise NotImplementedError, "#{self.class} has not implemented method '#{__method__}'"; end
  def background_image_mobile_attachment; raise NotImplementedError, "#{self.class} has not implemented method '#{__method__}'"; end
end