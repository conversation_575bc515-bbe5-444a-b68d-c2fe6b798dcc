class PageCompositionService
  def initialize(page)
    @page = page
    @layout = page.layout
  end

  def call(scope: :full)
    case scope
    when :full
      full_composition
    when :content_only
      content_blocks
    else
      []
    end
  end

  private

  def full_composition
    return content_blocks unless @layout

    layout_service = LayoutCompositionService.new(@layout)
    layout_models = layout_service.call

    [
      layout_models[:main_header],
      *layout_models[:global_headers],
      *content_blocks, # <PERSON><PERSON>žíme obsah stránky mezi <PERSON>aví a patičku
      *layout_models[:global_footers],
      layout_models[:main_footer]
    ].flatten.compact
  end

  def content_blocks
    @page.blocks.visible.includes(:controls)
  end
end