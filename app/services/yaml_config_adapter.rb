class YamlConfigAdapter < ConfigAdapter
  def initialize(identifier)
    @config = BlockConfigLoader.load(identifier)
    @identifier = identifier
  end

  def id
    SecureRandom.hex(3)
  end

  def name
    @config[:name] || @identifier # Pokud váš YAML loader nastaví @config[:name] z nejvyšší úrovně
  end

  def containers
    binding.irb
    @config[:containers].map do |key, value|
      BlockContainerData.build_from_hash(value)
    end
  end

  def controls_data
    (@config[:controls] || []).map do |control_data|
      ControlRegistry.class_for(control_data[:type])
                     .build_from_yaml(control_data)
    end
  end

  def media_items
    return nil if @config[:media_items].nil?

    media_items = []
    @config[:media_items].each do |media_item|
      media_items << MediaItemData.new(
        id: SecureRandom.hex(3),
        title: media_item[:title],
        text: media_item[:text],
        published_at: media_item[:published_at],
        image_url: media_item[:image_url],
        **media_item[:custom_fields] || {}
      )
    end

    media_items
  end

  def pricing_sections_data
    [
      {
        name: "<PERSON><PERSON><PERSON><PERSON>",
        pricing_items: [
          { name: "Polévka 1", price: 100, price_eur: 4, is_price_textual?: false, price_textual: nil, description: "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more re", duration: 30, image_url: nil },
          { name: "Polévka 2", price: 100, price_eur: 4, is_price_textual?: true, price_textual: "Cena dohodou", description: "Popis polévky 2", duration: 30, image_url: '/images/previews/w/2.jpg' }
        ]
      },
      {
        name: "Hlavní chody",
        pricing_items: [
          { name: "Chod 1", price: 100, price_eur: 4, is_price_textual?: false, price_textual: nil, description: "Popis chodu 1", duration: 30, image_url: '/images/previews/w/3.jpg' },
          { name: "Chod 2", price: 100, price_eur: 4, is_price_textual?: false, price_textual: nil, description: "Popis chodu 2", duration: 30, image_url: '/images/previews/w/4.jpg' }
        ]
      }
    ]
  end

  def pricing_data
    sections = pricing_sections_data.map do |section_data|
      items = section_data[:pricing_items].map do |item_data|
        PricingItemData.new(**item_data)
      end

      PricingSectionData.new(name: section_data[:name], items: items)
    end

    PricingData.new(name: "Naše menu", sections: sections)
  end

  def media_options
    return nil if @config[:media_options].nil?

    MediaOptionsData.new(
      posts_limit: @config[:media_options][:posts_limit],
      position: @config[:media_options][:position],
      gap: @config[:media_options][:gap],
      type: @config[:media_options][:type],
      layout: @config[:media_options][:layout]
    )
  end

  def pricing_id
    @config[:pricing_id] # Vrací nil, pokud není v @config
  end

  def pricing_options
    (@config[:pricing_options] || {}).deep_symbolize_keys
  end

  def background_image_attachment
    nil # YAML typicky neobsahuje přímé ActiveStorage přílohy
  end

  def background_image_mobile_attachment
    nil # YAML typicky neobsahuje přímé ActiveStorage přílohy
  end
end