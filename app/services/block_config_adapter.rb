# app/services/block_config_adapter.rb
class BlockConfigAdapter < ConfigAdapter
  def initialize(block_record) # Přejmenováno z 'block' na 'block_record' pro jasnost
    @block = block_record
  end

  def id
    @block.id
  end

  def name
    @block.name
  end

  def options
    (@block.options || {}).deep_symbolize_keys
  end

  def controls_data
    @block.controls.map do |control_data|
      ControlRegistry.class_for(control_data[:type])
                     .build_from_record(control_data)
    end
  end

  def containers
    @block.block_containers.map do |block_container|
      BlockContainerData.build_from_block_container(block_container)
    end
  end

  def pricing_data
    return {} unless @block.pricing

    sections = @block.pricing.pricing_sections.includes(:pricing_items).map do |section_data|
      items = section_data.pricing_items.map do |item_data|
        PricingItemData.new(
          name: item_data.name,
          price: item_data.price,
          price_eur: item_data.price_eur,
          is_price_textual?: item_data.is_price_textual,
          content_present?: item_data.content_present?,
          price_textual: item_data.price_textual,
          description: item_data.description,
          duration: item_data.duration,
          image_url: nil,
          slug: item_data.slug,
          content: item_data.content
        )
      end

      PricingSectionData.new(name: section_data.name, items: items)
    end

    PricingData.new(name: "Naše menu", sections: sections)
  end

  def media_items
    return nil if @block.media.empty?

    media_items = []
    @block.media.limit(@block.media_posts_limit).each do |media_item|
      media_items << MediaItemData.new(
        id: media_item.id,
        title: media_item.title,
        text: media_item.text,
        icon: media_item.icon,
        published_at: media_item.published_at,
        image_attachment: media_item.image.attached? ? media_item.image : nil,
        **media_item[:custom_fields] || {}
      )
    end

    media_items
  end

  def media_options
    MediaOptionsData.new(
      posts_limit: @block.media_posts_limit,
      position: @block.media_position,
      gap: @block.media_gap,
      type: @block.media_type&.slug,
      layout: @block.media_layout
    )
  end

  def pricing_id
    @block.pricing_id
  end

  def pricing_options
    (@block.pricing_options || {}).deep_symbolize_keys
  end

  def background_image_attachment
    @block.background_image if @block.background_image.attached? # Kontrola attached? je dobrá
  end

  def background_image_mobile_attachment
    @block.background_image_mobile if @block.background_image_mobile.attached?
  end
end