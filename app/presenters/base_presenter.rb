class BasePresenter
  attr_reader :config, :context

  def initialize(config, context)
    @config = config
    @context = context
  end

  def position
    @config[:position]
  end

  def configure(_config)
    raise NotImplementedError, "Subclasses must implement the #configure method"
  end

  def type
    raise NotImplementedError, "Subclasses must implement the #type method"
  end

  def options
    {}
  end

  def component
    raise NotImplementedError, "Subclasses must implement the #component method"
  end
end