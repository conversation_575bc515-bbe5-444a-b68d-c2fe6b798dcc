class BlockPresenter
  attr_reader :id, :name, :containers, :position, :media_items, :media_options, :controls, :view_context, :pricing_data, :background_image, :background_image_mobile
  attr_reader :outer_container_layer, :block_container_data, :content_layer, :media_layer

  def initialize(id:, name:, containers:, position:, view_context:, controls:, media_items:, media_options:, pricing_data:, background_image: nil, background_image_mobile: nil)
    @id = id
    @name = name
    @containers = containers
    @view_context = view_context
    @controls = controls
    @media_items = media_items
    @media_options = media_options
    @pricing_data = pricing_data
    @background_image = background_image
    @background_image_mobile = background_image_mobile
    @component = nil
    @position = position
  end

  def context
    @view_context
  end

  def media_alignment_class
    if container(:media_block_container).media_alignment.present?
      container(:media_block_container).media_alignment
    else
      "order-first"
    end
  end

  def media_position_class
    @media&.options&.fetch(:position, nil) == "right" ? "order-last" : "order-first"
  end

  def media_container_dom_id
    "block-#{id}-media-layer"
  end

  def component_class
    Rails.application.config.x.components[@name.to_sym].safe_constantize
  end

  def component
    @component ||= component_class.new(block_presenter: self, context: view_context)
  end

  def content_theme
    content_layer.theme || container(:inner_block_container).theme_class || outer_container_layer.theme_class
  end

  def form
    @form = Form.take
  end

  def containers_by_type
    @containers.each_with_object({}) do |container, hash|
      hash[container.type.underscore.to_sym] = container
    end
  end

  def container(type)
    containers_by_type[type]
  end
end