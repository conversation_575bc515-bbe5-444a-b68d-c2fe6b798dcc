<% @theme[:colors].each do |name, attrs| %>
  <div data-theme="<%= name %>" class="p-12">
    <h1 class="text-2xl text-base-content">Hello <i><%= name %></i></h1>

    <p class="text-base-content">Theme <strong><%= name %></strong> preview</p>

    <div class="mt-2 flex items-center space-x-3">
      <button class="btn btn-accent">Submit me!</button>
      <button class="btn btn-outline btn-accent">Outline</button>
      <a href="#" class="link link-hover text-base-content">Link</a>
    </div>

    <div class="mt-3 grid grid-cols-8 gap-4">
      <% (1..8).each do |item| %>
        <div class="mt-3 bg-base-200/80 p-4 rounded-box text-center">
          <strong>Grid item <%= item %></strong>
        </div>
      <% end %>
    </div>
  </div>
<% end %>