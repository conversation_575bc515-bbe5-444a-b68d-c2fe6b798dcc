module BlockControls
  class DividerControl < Phlex::HTML
    include Phlex::Rails::Helpers::Tag
    include Phlex::Rails::Helpers::SimpleFormat
    include Phlex::Rails::Helpers::Sanitize

    register_element :block_control

    def initialize(control)
      @control = control
      @id = control.id
      @width = @control.width
      @height = @control.height
    end

    def view_template
      block_control(id: %(control-#{@id}), class: "#{@control.width} #{@control.height} bg-accent mx-auto rounded-full control-container")
    end
  end
end
