module BlockControls
  class ParagraphControl < Phlex::HTML
    include Phlex::Rails::Helpers::Tag
    include Phlex::Rails::Helpers::SimpleFormat
    include Phlex::Rails::Helpers::Sanitize

    register_element :block_control

    def initialize(paragraph_block_control_object)
      @control = paragraph_block_control_object
      @text = @control.text
      @id = @control.id
    end

    def view_template
      block_control(id: %(control-#{@id}), class: "control-container #{@control&.options&.[](:css)}") {  @text.html_safe } if @text.present?
    end
  end
end
