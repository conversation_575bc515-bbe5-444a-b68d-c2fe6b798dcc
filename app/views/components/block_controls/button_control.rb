module BlockControls
  class ButtonControl < ApplicationComponent
    register_element :block_control
    include Phlex::Rails::Helpers::Tag
    include <PERSON><PERSON><PERSON><PERSON>

    def initialize(button_block_control_object)
      @control = button_block_control_object

      @primary_button = @control.primary_button_data
      @secondary_button = @control.secondary_button_data
    end

    def view_template
      block_control(
        id: %(control-#{@id}),
      ) do
        div(class: "flex space-x-4 items-center") do
          a(
            href: primary_page_link,
            class: "btn btn-accent #{"hidden" if @primary_button.text.empty?}",
            id: "control-#{@control.id}-primary-button-text"
          ) { @primary_button.text } if admin? || (@primary_button.text.present? && primary_page_link)
          a(
            href: secondary_page_link,
            class: "link link-hover text-base-content #{"hidden" if @secondary_button.text.empty?}",
            id: "control-#{@control.id}-secondary-button-text"
          ) { @secondary_button.text } if admin? || (@secondary_button.text.present? && secondary_page_link)
        end
      end
    end

    private

    def primary_page_link
      if @primary_button.link_type == "link"
        @primary_button.link
      else
        page_path_resolver(Page.find(@primary_button.page_id))
      end
    rescue ActiveRecord::RecordNotFound
      nil
    end

    def secondary_page_link
      if @secondary_button.link_type == "link"
        @secondary_button.link
      elsif @secondary_button.link_type == "phone"
        "tel:#{@secondary_button.phone}"
      else
        page_path_resolver(Page.find(@secondary_button.page_id))
      end
    rescue ActiveRecord::RecordNotFound
      nil
    end
  end
end
