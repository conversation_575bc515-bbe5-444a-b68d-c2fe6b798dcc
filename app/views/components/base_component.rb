class BaseComponent < ApplicationComponent
  include Ui

  attr_reader :block_presenter

  def initialize(block_presenter:, context:)
    @block_presenter = block_presenter # @block nyní obsahuje instanci BlockPresenter
    @context = context
  end

  def container(css: nil, style: nil, &block)
    Ui::OuterContainer(block_presenter, css:, style:, &block)
  end

  def media_position
    "first"
  end

  def overlay
    Ui::Overlay(block_presenter)
  end

  def inner_container(css: nil, &block)
    Ui::InnerContainer(block_presenter, css:, &block)
  end

  def content_container(css: nil, &block)
    Ui::ContentContainer(block_presenter, css: css, &block)
  end

  def media_container(css: nil, &block)
    Ui::MediaContainer(block_presenter, css: css, &block)
  end
end
