module BlockViews
  class HeaderViews::Header001Component < BaseComponent
    include Phlex::Rails::Helpers::ImageTag

    def view_template
      div(class: fixed) do
        container do
          inner_container(css: "bg-transparent") do
            header(class: "mx-auto w-full flex items-center justify-between") do
              div(class: "flex") do
                if Current.website.logo.variable?
                elsif Current.website.logo.attached?
                  a(href: "/", class: "#{Current.website.logo_size_class} flex") do
                    image_tag Current.website.logo, class: "w-auto object-cover"
                  end
                else
                  a(href: root_path) do
                    h4(class: "font-medium text-lg sm:text-xl") { Current.website.name }
                  end
                end
              end
              nav(class: "flex items-center justify-between") do
                render Ui::Navigation.new(
                  pages: @context.pages_for_nav,
                  current_page: @context.current_page,
                  context_type: @context.type
                )
              end if @context.pages_for_nav&.any?
            end
          end
        end
      end
    end

    def fixed
      if @block_presenter.view_context.type != :admin
        "w-full top-0 #{@context.current_page&.is_homepage ? "fixed" : "sticky shadow"} z-50"
      end
    end
  end
end