module BlockViews
  class HeaderViews::Header002Component < BaseComponent
    include Phlex::Rails::Helpers::ImageTag

    def view_template
      @block_presenter.block_container_data.layout = nil # hack
      container do
        inner_container do
          header(class: "hidden mx-auto md:flex items-center justify-between p-3") do
            nav(class: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative") do
              div(class: "lg:flex items-center justify-center") do
                div(class: "flex items-center gap-x-3") do
                  render Ui::Navigation.new(
                    pages: pages_on_left,
                    current_page: @context.current_page
                  )
                end if @context.pages_for_nav
                div(class: "relative") do
                  div(class: "bg-white rounded") do
                    if Current.website.logo.attached?
                      a(
                        href: root_path,
                        class: %(#{Current.website.logo_size_class} flex)
                      ) { image_tag Current.website.logo, class: "w-auto object-cover" }
                    end
                  end
                end
                div(class: "flex items-center gap-x-3") do
                  render Ui::Navigation.new(
                    pages: pages_on_right,
                    current_page: @context.current_page
                  )
                end if @context.pages_for_nav
              end
            end
          end

          header(class: "md:hidden", data_controller: "hamburger-menu") do
            # Horní lišta s logem a tlačítkem
            div(class: "flex items-center justify-between p-3 border-b border-base-200") do
              # Logo (vaše stávající logika)
              div(class: "flex lg:flex-1") do
                if Current.website.logo.variable?
                elsif Current.website.logo.attached?
                  a(href: "/", class: "#{Current.website.logo_size_class} flex") do
                    image_tag Current.website.logo, class: "w-auto object-cover"
                  end
                else
                  span(class: "text-4xl font-medium") { Current.website.name }
                end
              end

              div(
                data_controller: "dropdown",
                data_action:
                  "click@window->dropdown#hide touchstart@window->dropdown#hide keydown.up->dropdown#previousItem keydown.down->dropdown#nextItem keydown.esc->dropdown#hide",
                class: "relative lg:hidden"
              ) do
                
                button(
                  data_action: "dropdown#toggle:stop",
                  type: "button",
                  class:
                    "text-xs flex flex-col -m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700"
                ) do
                  
                  svg(
                    class: "h-6 w-6",
                    fill: "none",
                    viewbox: "0 0 24 24",
                    stroke_width: "1.5",
                    stroke: "currentColor",
                    aria_hidden: "true"
                  ) do |s|
                    s.path(
                      stroke_linecap: "round",
                      stroke_linejoin: "round",
                      d: "M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"
                    )
                  end
                  
                  span(class: "uppercase font-medium") { "Menu" }
                  
                end
                div(
                  data_dropdown_target: "menu",
                  class:
                    "hidden w-full fixed inset-y-0 right-0 z-50 flex flex-col justify-between overflow-y-auto bg-white sm:max-w-sm sm:ring-1 sm:ring-gray-900/10 transform opacity-0 scale-95"
                ) do
                  div(class: "p-6") do
                    div(class: "flex items-center justify-between") do
                      
                      a(
                        href: "/",
                        class:
                          "space-x-2 inline-flex items-center -m-1.5 p-1.5 text-lg font-medium"
                      ) do
                        
                        svg(
                          xmlns: "http://www.w3.org/2000/svg",
                          fill: "none",
                          viewbox: "0 0 24 24",
                          stroke_width: "1.5",
                          stroke: "currentColor",
                          class: "size-6"
                        ) do |s|
                          s.path(
                            stroke_linecap: "round",
                            stroke_linejoin: "round",
                            d: "M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"
                          )
                        end
                        
                        span { "Menu" }
                        
                      end
                      
                      button(
                        data_action: "dropdown#toggle:stop",
                        type: "button",
                        class: "-m-2.5 rounded-md p-2.5 text-gray-700 hover:bg-gray-50"
                      ) do
                        
                        svg(
                          class: "h-6 w-6",
                          fill: "none",
                          viewbox: "0 0 24 24",
                          stroke_width: "1.5",
                          stroke: "currentColor",
                          aria_hidden: "true"
                        ) do |s|
                          s.path(
                            stroke_linecap: "round",
                            stroke_linejoin: "round",
                            d: "M6 18L18 6M6 6l12 12"
                          )
                        end
                        
                      end
                    end
                    div(class: "mt-3 flow-root border-t border-gray-200 pt-3") do
                      div(class: "divide-y divide-gray-500/10") do
                        div(class: "py-3") do
                          render Ui::Navigation.new(
                            pages: @context.pages_for_nav,
                            current_page: @context.current_page,
                            type: :mobile
                          )
                        end
                      end
                    end
                  end
                end
              end
            end

          end

        end
      end
    end

    private

    def pages_on_left
      memoized_split_pages.first
    end

    def pages_on_right
      memoized_split_pages.last
    end

    def memoized_split_pages
      @memoized_split_pages ||= begin
                                  all_pages = @context.pages_for_nav.to_a
                                  split_at = (all_pages.length / 2.0).ceil
                                  left, right = all_pages.each_slice(split_at).to_a
                                  [left.to_h, right.to_h]
                                end
    end

    def page_path_resolver(page)
      return "#" if controller.present? && admin?
      return page.link if page.is_link?
      return homepage_path if page.is_homepage

      if page.is_anchor? && page.anchor_block.present?
        return "#block-#{page.anchor_block.id}" if page.is_anchor? && page.anchor_block.present?
        return "/#block-#{page.anchor_block.id}" if @current_page != page
      end

      locale = I18n.locale || :cs

      if I18n.locale == Current.website.locale.to_sym
        locale = nil
      end

      page_path(page, locale:)
    end
  end
end