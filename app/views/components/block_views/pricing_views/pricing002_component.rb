module BlockViews
  class PricingViews::Pricing002Component <  BaseComponent
    include BlockControls
    include ApplicationHelper
    include Phlex::Rails::Helpers::NumberToCurrency

    def component_allowed?
      true
    end

    def view_template
      container do
        inner_container do
          content_container

          div(class: "flex flex-col space-y-5") do
            @block_presenter.pricing_data.sections.each do |section|
              div do
                h3(class: "text-2xl font-bold tracking-tight text-gray-900 px-4 sm:px-6 lg:px-8") { section.name }

                div(class: "mt-6 px-4 sm:px-6 lg:px-8") do
                  ul(
                    role: "list",
                    class: "grid grid-cols-1 gap-x-6 gap-y-10 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 xl:gap-x-4"
                  ) do
                    section.items.each do |item|
                      render_item(item) # Voláme novou metodu pro vykreslení karty
                    end
                  end
                end
              end
            end
          end
        end
      end
    end

    private

    def render_item(item)
      li(class: "group relative flex flex-col overflow-hidden rounded-lg bg-white hover:shadow-md transition-shadow duration-300") do
        div(class: "aspect-h-7 aspect-w-10 bg-gray-200 sm:aspect-none sm:h-48") do
          img(
            src: item.image_url,
            alt: item.name,
            class: "h-full w-full object-cover object-center sm:h-full sm:w-full"
          )
        end

        div(class: "flex flex-1 flex-col space-y-4 p-4") do
          div(class: "flex items-start justify-between") do
            h4(class: "text-base font-semibold text-gray-900") do
              a(href: "#") do
                span(class: "absolute inset-0", aria_hidden: "true")
                plain item.name
              end
            end

            if item.duration&.positive?
              p(class: "ml-2 flex-shrink-0 rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800") do
                "#{item.duration} min"
              end
            end
          end

          p(class: "text-sm text-gray-500 line-clamp-3") { item.description }

          div(class: "flex-grow") {}

          div(class: "mt-4 pt-4 border-t border-gray-100") do
            dl(class: "flex items-center justify-between") do
              dt(class: "sr-only") { "Cena" } # Jen pro screen readery
              # Formátovaná cena
              dd(class: "text-lg font-bold text-gray-900") { price_formatted(item) }

              if item.price_eur
                dt(class: "sr-only") { "Cena v EUR" }
                dd(class: "text-sm font-medium text-gray-500") { "#{item.price_eur} €" }
              end
            end
          end
        end
      end
    end

    def price_formatted(item)
      "#{item.price} Kč"
    end
  end
end
