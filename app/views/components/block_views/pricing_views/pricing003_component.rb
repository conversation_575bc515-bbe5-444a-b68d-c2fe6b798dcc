# frozen_string_literal: true

module BlockViews
  class PricingViews::Pricing003Component < BaseComponent
    include BlockControls
    include Phlex::Rails::Helpers::NumberToCurrency
    include Phlex::Rails::Helpers::LinkTo

    def view_template
      container do
        inner_container(css: "px-5 flex flex-col") do
          content_container

          div(class: "w-full") do
            div(class: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8") do
              @block_presenter.pricing_data.sections.first&.items&.each_with_index do |item, index|
                render_item_card(item, index)
              end
            end
          end

          media_container do
            @block_presenter.pricing_data.sections.first&.items&.each_with_index do |item, index|
              render_item_card(item, index)
            end
          end
        end
      end
    end

    private

    def render_item_card(item, index)
      div(class: "animate-fade-in", style: "animation-delay: #{index * 0.1}s;") do
        div(class: "h-full transition-all duration-300 hover:shadow hover:-translate-y-1 bg-white border-2 border-accent/10 rounded-lg flex flex-col") do
          header(class: "p-6 pb-4") do
            div(class: "flex items-start gap-2") do
              div(class: "p-2 bg-accent/10 rounded-lg") do
                svg(xmlns: "http://www.w3.org/2000/svg", fill: "none", viewbox: "0 0 24 24", stroke_width: "1.5", stroke: "currentColor", class: "text-accent/50 size-6") do |s|
                  s.path(stroke_linecap: "round", stroke_linejoin: "round", d: "M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12Z")
                end
              end
              div(class: "flex-1") do
                h3(class: "tracking-tight text-lg font-semibold text-accent leading-tight") { item.name }
                div(class: "text-sm text-base-content line-clamp-2") { item.description }
              end
            end

            div(class: "mt-4") do
              div(class: "text-3xl font-bold text-accent") { render_price(item) }
            end
          end

          div(class: "p-6 pt-0 mt-auto") do
            link_to(pricing_item_path(slug: item.slug), class: "w-full bg-accent text-accent-content hover:bg-accent/90 transition-all duration-300 inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium h-10 px-4 py-2") do
              "Zjistit více"
            end
          end if item.slug.present? && item.content.present?
        end
      end
    end

    # Vykreslí cenu na základě toho, zda je textová nebo číselná
    def render_price(item)
      if item.is_price_textual? && item.price_textual.present?
        return item.price_textual
      end

      if item.price.present?
        if I18n.locale == :cs
          number_to_currency(item.price, unit: "Kč", separator: ",", delimiter: " ", format: "%n %u")
        else
          number_to_currency(item.price_eur, unit: "€", separator: ",", delimiter: " ", format: "%n %u")
        end
      else
        "Cena na dotaz" # Fallback, pokud cena chybí
      end
    end
  end
end
