module BlockViews
  class PricingViews::Pricing001Component <  BaseComponent
    include BlockControls
    include ApplicationHelper
    include Phlex::Rails::Helpers::NumberToCurrency

    def component_allowed?
      true
    end

    def view_template
      container do
        inner_container do
          content_container

          @block_presenter.pricing_data.sections.each do |section|
            div(class: "flex flex-col space-y-2 w-full") do
                h4 { section.name }
                div(class: "px-3") do
                  ul(role: "list", class: "list mt-2") do
                    section.items.each do |item|
                      render_item(item)
                    end
                  end
                end
            end
            end
          end
        end
    end

    private

    def border_for_item(item)
      "item after:border-base-content/40" if item.price
    end

    def render_item(item)
      li(
        class:
          "flex flex-wrap items-center justify-between px-2 gap-y-4 py-2 sm:flex-nowrap "
      ) do
        div(class: border_for_item(item)) do
          div(class: "flex space-x-2 items-center") do
            span(class: "text-sm font-semibold leading-6 text-gray-900") { item.name }
            dd(class: "flex text-xs text-gray-500 items-center space-x-1") do
              span { "#{item.duration}min" }
            end if item.duration&.positive?
          end
          div(class: "mt-0.5 flex items-center gap-x-2 text-sm leading-5 text-gray-600") do
            p { item.description }
          end
        end
        dl(
          class:
            "flex w-full flex-none justify-between gap-x-8 sm:w-auto"
        ) do
          div(class: "flex items-center text-sm leading-3 space-x-5") do
            dd(class: "text-sm leading-3 font-medium") do
              price_formatted item
            end
          end
        end
      end
    end
  end
end
