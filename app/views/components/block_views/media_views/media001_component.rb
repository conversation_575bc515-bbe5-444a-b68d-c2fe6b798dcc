module BlockViews
  class MediaViews::Media001Component < BaseComponent
    def view_template
      container do
        inner_container do
          content_container(css: "mx-auto flex text-center justify-center flex-col")
          hr(class: "my-8 h-1 w-full border-accent/40")
            media_container(css: "mt-12") do
              render Ui::Gallery.new(
                block_presenter.media_items,
                layout: block_presenter.media_options.layout || :grid,
                gap: block_presenter.media_options.gap || 3,
                resize_options: resize_image_options,
                item_classes: "relative aspect-square overflow-hidden rounded-md"
              )
            end
        end
      end
    end

    private

    def resize_image_options
      { resize_to_fill: [200, 200] }
    end
  end
end
