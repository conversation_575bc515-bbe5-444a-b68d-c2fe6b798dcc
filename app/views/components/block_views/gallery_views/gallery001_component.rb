module BlockViews
  class GalleryViews::Gallery001Component < BaseComponent
    def view_template
      container do
        overlay
        inner_container(css: "py-8 overflow-hidden sm:grid sm:grid-cols-2 items-center") do
          content_container

          div(class: "order-1 lg:order-2") do
            div(class: "relative") do
              div(
                class:
                  "absolute inset-0 bg-gradient-to-br from-accent/20 to-accent/30 rounded-2xl transform rotate-3"
              )
              div(class: "relative bg-white p-6 rounded-2xl shadow-lg") do
                media_container
              end
            end
          end
        end
      end
    end
  end
end
