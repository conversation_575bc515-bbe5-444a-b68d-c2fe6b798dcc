module BlockViews
  class ContactViews::Contact002Component < BaseComponent
    include <PERSON><PERSON>s<PERSON>elper

    def view_template
      container do
        inner_container(css: "px-4 grid grid-cols-2") do
          content_container(css: "col-span-2")
            div do
              div(data: { theme: "soft" }, class: "rounded-lg border-2 border-accent/20") do
                div(class: "px-6 py-4") do
                  Ui::ContactForm(form: @block_presenter.form)
                end
              end
            end

            div(class: "@container flex flex-col #{@block_presenter.container(:inner_block_container).gap}") do
              div(class: "rounded-lg border text-card-foreground shadow-sm bg-white/10 backdrop-blur-sm border-accent/20") do
                div(class: "p-8") do
                  h3(class: "text-2xl font-bold mb-6 text-base-content") do
                    "Kontaktní údaje"
                  end
                  media_container(css: "bg-transparent flex flex-col space-y-5") do
                    @block_presenter.media_items.each do |media_item|
                      render_media_item(media_item)
                    end
                  end
                end
              end
              div(class: "rounded-lg border text-card-foreground shadow-sm bg-white/10 backdrop-blur-sm border-white/20") do
                div(class: "p-8") do
                  h3(class: "text-xl font-bold mb-4 text-white") do
                    "Služby v oblasti"
                  end
                  p(class: "text-white/90 text-lg mb-4") { "Praha a okolí" }
                  div(class: "border-t border-white/20 pt-4") do
                    p(class: "text-white/80") do
                      " Pro objednání služeb nebo dotazy mě neváhejte kontaktovat. Odpovím vám co nejdříve."
                    end
                  end
                end
              end
            end
        end
      end
    end
    def render_media_item(item)
      div(class: "space-y-6") do
        div(class: "flex space-x-4") do
          div do
            div(class: "p-3 bg-accent/20 rounded-lg") do
              render_icon(item.icon&.name, size: 6)
            end
          end

          div do
            h4(class: "font-bold text-lg mb-1 text-base-content") { item.title }
            p(class: "text-base-content/90 text-lg") { item.text }
          end
        end
      end
    end
  end
end
