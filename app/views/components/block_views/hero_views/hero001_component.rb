module BlockViews
  class HeroViews::Hero001Component < BaseComponent
    def view_template
      container(css: "relative min-h-screen text-white overflow-hidden") do
        overlay

        inner_container(css: "flex items-center justify-center min-h-screen bg-transparent") do
          content_container(css: "bg-transparent")
        end

        div(class: "hidden sm:block absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce z-10") do
          div(class: "w-6 h-10 border-2 border-accent rounded-full flex justify-center"
          ) { div(class: "w-1 h-3 bg-accent rounded-full mt-2 animate-pulse") }
        end if @block_presenter.position == 1
      end
    end

    private

    def background_image
      # return url_for(@block_object.background_image) if @block_object.background_image.respond_to?(:attached?) && @block_object.background_image.attached?
      return item_image_url(@block_object.media.items.first) if @block_object.media&.items&.first

      # Default fallback image
      "https://images.unsplash.com/photo-1490645935967-10de6ba17061?q=80&w=3506&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
    end

    def item_image_url(item)
      return url_for(item.image) if item.respond_to?(:image) && item.image.respond_to?(:attached?) && item.image.attached?
      return item.image if item.respond_to?(:image) && item.image.is_a?(String) && item.image.present?
      return item.image_url if item.respond_to?(:image_url) && item.image_url.present?
      return item.data[:image] if item.respond_to?(:data) && item.data.is_a?(Hash) && item.data[:image].present?

      # Default fallback image
      "https://images.unsplash.com/photo-1490645935967-10de6ba17061?q=80&w=3506&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
    end
  end
end
