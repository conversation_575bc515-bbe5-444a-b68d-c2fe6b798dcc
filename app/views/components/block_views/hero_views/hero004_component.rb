module BlockViews
  class HeroViews::Hero004Component < BaseComponent
    def view_template
      container do
        inner_container do
          media_container do
            Ui::Gallery(
              @block_presenter.media_items,
              layout: :grid,
              gap: 3,
              resize_options: { resize_to_fill: [400, 300] },
              item_classes: "rounded-box relative aspect-[4/3] overflow-hidden")
          end

            div(class: "grid grid-cols-1 gap-y-8 sm:gap-y-0 sm:grid-cols-2 sm:gap-x-8") do
              div(class: "flex-1 min-w-1/2 flex flex-col order-2") do
                content_container
            end
          end
        end
      end
    end
  end
end
