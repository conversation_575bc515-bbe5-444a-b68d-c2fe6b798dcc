header(
  data_theme: "neutral",
  class: "relative min-h-screen text-white overflow-hidden"
) do
  comment { "Background Image" }
  div(
    class: "absolute inset-0 bg-cover bg-center bg-no-repeat",
    style: "background-image:url('/images/radka.jpg')"
  ) { div(class: "absolute inset-0 bg-base-100/70") }
  comment { "Decorative Blurs" }
  div(class: "absolute inset-0") do
    div(
      class:
        "absolute top-20 left-10 w-32 h-32 bg-accent/10 rounded-full blur-xl"
    )
    div(
      class:
        "absolute bottom-20 right-10 w-48 h-48 bg-secondary/10 rounded-full blur-2xl"
    )
    div(
      class:
        "absolute top-1/2 left-1/4 w-24 h-24 bg-accent/5 rounded-full blur-lg"
    )
  end
  comment { "Content" }
  div(
    class:
      "relative container mx-auto px-4 flex items-center justify-center min-h-screen z-10"
  ) do
    div(class: "text-center max-w-4xl") do
      comment { "Title" }
      h1(class: "text-5xl md:text-7xl font-bold mb-6 animate-fade-in") do
        span(class: "block text-accent") { "RADKA" }
        span(class: "block text-base-content mt-2") { "RAČANSKÁ" }
      end
      comment { "Subtitle" }
      div(class: "mb-8") do
        p(
          class:
            "text-2xl md:text-3xl text-white/90 font-light mb-4 drop-shadow-md"
        ) { " PORODNÍ ASISTENTKA" }
        div(class: "w-24 h-1 bg-accent mx-auto rounded-full")
      end
      comment { "Description" }
      p(
        class:
          "text-lg md:text-xl text-white/80 max-w-2xl mx-auto mb-12 leading-relaxed drop-shadow-sm"
      ) do
        " Doprovázím ženy na jejich cestě mateřstvím s láskou, respektem a odborností. Každá žena si zaslouží individuální péči a podporující prostředí."
      end
      comment { "Buttons" }
      div(class: "flex flex-col sm:flex-row gap-4 justify-center") do
        button(
          class:
            "btn btn-accent btn-lg text-accent-content rounded-full font-semibold hover:bg-accent/90 transition-all duration-300 hover:scale-105 shadow-lg"
        ) { " Zobrazit služby " }
        button(
          class:
            "btn btn-outline btn-lg btn-accent rounded-full font-semibold hover:bg-accent hover:text-accent-foreground transition-all duration-300 hover:scale-105 backdrop-blur-sm"
        ) { " Kontakt " }
      end
    end
  end
  comment { "Scroll Indicator" }
  div(
    class:
      "hidden sm:block absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce z-10"
  ) do
    div(
      class: "w-6 h-10 border-2 border-accent rounded-full flex justify-center"
    ) { div(class: "w-1 h-3 bg-accent rounded-full mt-2 animate-pulse") }
  end
end