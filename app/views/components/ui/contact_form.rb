# app/components/ui/contact_form.rb
class Ui::ContactForm < ApplicationComponent
  # <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, že komponentě můžeme předat i případné chyby z neúspěšného o<PERSON>ání
  def initialize(form: nil, errors: {})
    @form = form
    @errors = errors
  end

  def view_template
    if @form.present?
        h2(class: 'card-title text-2xl mb-1') { @form.name }
        p(class: 'text-base-content/70 mb-6') { @form.description }

        render_form
    else
      div(class: 'text-center p-8') { "Formulář nebyl nalezen." }
    end
  end

  private

  def render_form
    form action: "#", method: :post, class: 'space-y-4' do
      #      input type: "hidden", name: "authenticity_token", value: helpers.form_authenticity_token

      @form.form_fields.each do |field|
        render_field(field)
      end

      div class: 'mt-4 card-actions' do
        button type: 'submit', class: 'btn btn-accent w-full' do
          @form.try(:submit_text) || 'Odes<PERSON> zprávu'
        end
      end
    end
  end

  def render_field(field)
    label_text = field.label
    field_key = label_text.parameterize.underscore.to_sym
    input_name = "form_submission[data][#{label_text}]"
    field_id = "field_#{field.id}"
    has_error = @errors[field_key].present?

      fieldset class: 'fieldset m-0', for: field_id do
        legend(class: "fieldset-legend") do
          plain label_text
          span(class: 'label-text-alt text-error') { '*' } if field.required?
        end
        render_input_for(field, input_name, field_id, has_error)
      end

      if has_error
        label class: 'label' do
          span(class: 'label-text-alt text-error') { @errors[field_key].join(', ') }
        end
      end
  end

  def render_input_for(field, name, id, has_error)
    # Přidáme třídu 'input-error' pokud pole obsahuje chybu
    error_class = has_error ? ' input-error' : ''
    placeholder_text = "Zadejte váš #{field.label.downcase}"

    case field.field_type
    when 'email'
      input type: 'email', name: name, id: id, required: field.required?,
            placeholder: placeholder_text,
            class: 'input w-full' + error_class
    when 'textarea'
      textarea name: name, id: id, required: field.required?, rows: 4,
               placeholder: "Napište vaši zprávu...",
               class: 'textarea textarea-bordered w-full' + error_class
    when 'date'
      input type: 'date', name: name, id: id, required: field.required?,
            class: 'input input-bordered w-full' + error_class
    when 'checkbox'
      # Checkbox má specifické formátování
      div class: 'form-control w-auto' do
        label class: 'label cursor-pointer justify-start gap-4', for: id do
          input type: 'checkbox', name: name, id: id, required: field.required?,
                class: 'checkbox' + (has_error ? ' checkbox-error' : ' checkbox-primary')
          span(class: 'label-text') { field.label }
        end
      end
    else # 'text' a všechny ostatní jako výchozí
      input type: 'text', name: name, id: id, required: field.required?,
            placeholder: placeholder_text,
            class: 'input input-bordered w-full' + error_class
    end
  end
end