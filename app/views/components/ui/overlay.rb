# frozen_string_literal: true

class Ui::Overlay < ApplicationComponent
  attr_reader :block_id, :block_presenter, :outer_container_layer

  def initialize(block_presenter)
    @block_id = block_presenter.id
    @block_presenter = block_presenter
    @outer_container_layer = block_presenter.outer_container_layer
  end

  def view_template
    #div(id: overlay_dom_id, class: "absolute z-20 left-0 top-0 inset-0 w-full h-full #{@outer_container_layer.background_overlay_opacity}") do
    # if @block_presenter.background_image
    #   div(class: "absolute inset-0 -z-10 w-full h-full") do
    #     img(src: url_for(@block_presenter.background_image), class: "w-full h-full object-cover")
    #   end
    # else
    #   div(class: "absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80", aria_hidden: "true") do
    #     div(
    #       class: "relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-primary to-accent opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]",
    #       style: "clip-path: polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)"
    #     )
    #   end
    # end
    #end

    div(id: overlay_dom_id, class: "absolute inset-0 bg-cover bg-center bg-no-repeat", style: "background-image:url(#{url_for(@block_presenter.background_image)})"
    ) { div(class: "absolute inset-0 bg-base-100/70") } if @block_presenter.background_image
  end

  private

  def overlay_dom_id
    "block-#{@block_id}-overlay"
  end
end
