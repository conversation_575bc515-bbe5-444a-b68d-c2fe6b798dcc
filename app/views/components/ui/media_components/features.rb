class Ui::MediaComponents::Features < ApplicationComponent
  include <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

  def initialize(block_presenter)
    @block_presenter = block_presenter

    @options = @block_presenter.media_options
    @items = @block_presenter.media_items
    @gap = @options.gap
  end

  def view_template
    case @options.layout.to_sym
    when :default
      render_default
    when :cards
      render_cards
    when :icons
      render_icons
    when :grid
      render_grid
    else
      render_default
    end
  end

  private

  def render_default
    div(class: "grid grid-cols-1 sm:grid-cols-2 space-y-4 sm:space-y-0 md:grid-cols-#{@options.columns} gap-#{@options.gap}") do
      @items.each do |item|
        div(class: "feature relative sm:p-3 flex flex-col space-y-3") do
          div(class: "flex space-x-3 items-center") do
            span(class: "icon rounded p-2 bg-accent text-accent-content") { render_icon(item) }
            h4 { item_title(item) }
          end
          div(class: "text-sm", id: "media-#{item.id}-text") { item.text&.html_safe }
        end
      end
    end
  end

  def render_cards
    div(class: "grid gap-#{@gap} grid-cols-[repeat(auto-fit,_minmax(280px,_1fr))] items-stretch") do
      @items.each do |item|
        div(class: "feature p-6 rounded-box border border-accent/20 flex flex-col h-full") do
          div(class: "flex flex-col items-center text-center space-y-3 grow") do
            div do
              span(class: "inline-flex icon rounded-full w-9 h-9 flex justify-center items-center bg-accent text-accent-content") do
                render_icon(item)
              end
            end

            h4(class: "font-medium") { item_title(item) }

            p(class: "text-sm text-gray-600") { item.text.gsub(/\r?\n/, "<br>").html_safe }
          end
        end
      end
    end
  end


  def render_grid
    div(class: "grid grid-cols-2 gap-5 sm:gap-0 @2xl/media:grid-cols-4") do
      @items.each do |item|
        div(class: "flex flex-col items-center justify-center") do
          span(class: "@xl/media:text-3xl text-lg font-medium") { item.title }
          p(class: "text-center text-xs @xl/media:text-base") { item.text.gsub(/\r?\n/, "<br>").html_safe }
        end
      end
    end
  end

  def render_icons
    div(class: "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-5 gap-#{@gap}") do
      @items.each do |item|
        div(class: "feature relative text-center flex flex-col items-center space-y-4") do
          span(class: "icon rounded-full p-4 bg-primary-100") { render_icon(item, size: 6) }
          h4(class: "font-medium") { item_title(item) }
          p(class: "text-sm text-gray-600") { item.text.gsub(/\r?\n/, "<br>").html_safe }
        end
      end
    end
  end

  def render_icon(item, size: 5)
    icon_name = if item.respond_to?(:icon) && item.icon.present?
                  return item.icon.svg_html.html_safe
                elsif item.is_a?(Hash) && item[:icon].present?
                  item[:icon]
                elsif item.is_a?(Hash) && item[:icon_name].present?
                  item[:icon_name]
                else
                  "check"
                end

    Icon.by_name(icon_name, size: size)
  end

  def item_title(item)
    if item.respond_to?(:title) && item.title.present?
      item.title
    elsif item.is_a?(Hash) && item[:title].present?
      item[:title]
    else
      "Feature"
    end
  end
end
