class Ui::MediaComponents::Gallery < ApplicationComponent
  include Phlex::Rails::Helpers::L

  COLUMN_CLASSES = {
    1 => "grid-cols-1",
    2 => "grid-cols-2",
    3 => "grid-cols-2 sm:grid-cols-3",
    4 => "grid-cols-2 md:grid-cols-4",
    5 => "grid-cols-2 sm:grid-cols-3 lg:grid-cols-5",
    6 => "grid-cols-2 sm:grid-cols-3 lg:grid-cols-6",
    7 => "grid-cols-2 sm:grid-cols-4 lg:grid-cols-7",
    8 => "grid-cols-2 sm:grid-cols-4 lg:grid-cols-8",
    9 => "grid-cols-3 md:grid-cols-5 lg:grid-cols-9",
    10 => "grid-cols-3 md:grid-cols-5 lg:grid-cols-10"
  }.freeze

  def initialize(block_presenter)
    @block_presenter = block_presenter
    @items = block_presenter.media_items
    @layout = block_presenter.media_options.layout || :grid
    @columns = 1
    @gap = block_presenter.media_options.gap || 3
    @resize_options = {}
    @item_classes = ""
  end

  def view_template
    case @layout.to_sym
    when :grid
      render_grid
    when :masonry
      render_masonry
    when :slider
      render_slider
    when :strip
      render_strip
    when :instagram
      render_instagram
    else
      render_grid
    end
  end

  private

  def columns_class
    COLUMN_CLASSES.fetch(@columns, "grid-cols-2 sm:grid-cols-3")
  end

  def render_instagram
    div(class: "px-5 sm:px-0 grid grid-cols-2 md:grid-cols-2 lg:grid-cols-#{@columns} gap-4 sm:gap-6") do
      @items.each do |item|
        div(class: "group relative overflow-hidden rounded-2xl bg-base-200 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2") do
          div(class: "aspect-square overflow-hidden") do
            Ui::Image(item, resize_options: @resize_options, classes: "w-full h-full object-cover group-hover:scale-110 transition-transform duration-500")
          end

          div(class: "absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent transition-opacity duration-300") do
            div(class: "absolute bottom-0 left-0 right-0 p-6") do
              if item.published_at && item.published_at.is_a?(Time)
                div(class: "text-white/80 text-sm mb-2") do
                  I18n.l(item.published_at, format: :date)
                end
              end
              if item.title.present?
                p(class: "text-white text-sm leading-relaxed line-clamp-3") do
                  plain item.title.truncate(120)
                end
              end
            end
          end
          comment { "Instagram Icon" }
          div(
            class:
              "absolute top-4 right-4 w-8 h-8 bg-white/90 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          ) do
            svg(
              class: "w-5 h-5 text-pink-500",
              fill: "currentColor",
              viewbox: "0 0 24 24"
            ) do |s|
              s.path(
                d:
                  "M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"
              )
            end
          end
        end
      end
    end
  end

  def render_grid
    div(class: "grid grid-cols-2 #{columns_class} gap-#{@gap}") do
      @items.each do |item|
        div(class: @item_classes) do
          Ui::Image(item, resize_options: @resize_options, classes: "w-full h-full object-cover")
        end
      end
    end
  end

  def render_masonry
    div(class: "columns-1 sm:columns-2 md:columns-#{@columns} gap-#{@gap} space-y-#{@gap}") do
      @items.each do |item|
        div(class: "break-inside-avoid #{@item_classes}") do
          Ui::Image(item, resize_options: @resize_options, classes: "w-full h-auto object-cover")
        end
      end
    end
  end

  def render_slider
    if @block_presenter.view_context.type == :admin
      if @block_presenter.media_items.present?
        render Ui::Image(@block_presenter.media_items.first, resize_options: { resize_to_fill: [1000, 800] }, classes: "w-full h-full object-cover")
      else
        render Ui::Image("https://picsum.photos/800/600", classes: "w-full h-full object-cover")
      end
    else
      div(data: { controller: "splide", splide_per_page_value: @columns }, class: "relative splide") do
        div(class: "splide__track") do
          ul(class: "splide__list") do
            @items.each do |item|
              li(class: "splide__slide") do
                div(class: @item_classes) do
                  Ui::Image(item, resize_options: { resize_to_fill: [870, 700] }, classes: "w-full h-full object-cover")
                end
              end
            end
          end
        end
      end
    end
  end

  def render_strip
    columns = Array.new(@columns) { [] }
    @items.each_with_index do |item, index|
      columns[index % @columns] << item
    end

    div(class: "grid grid-cols-2 md:grid-cols-#{@columns} gap-#{@gap} items-start") do
      columns.each_with_index do |column_items, column_index|
        div(class: "flex flex-col space-y-#{@gap} even:mt-10") do

          column_items.each do |item|
            div(class: "relative rounded-box") do
              # Vaše resize options jsou v pořádku, obrázky budou stejně velké
              render Ui::Image(item, resize_options: { resize_to_fill: [200, 250] }, classes: "w-full rounded-box h-full object-cover")
            end
          end
        end
      end
    end
  end
end
