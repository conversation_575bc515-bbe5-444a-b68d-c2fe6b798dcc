# frozen_string_literal: true

class Ui::MediaContainer < ApplicationComponent
  attr_reader :block_id, :block_presenter, :css
  def initialize(block_presenter, css: nil)
    @block_id = block_presenter.id
    @block_presenter = block_presenter
    @block_container_data = block_presenter.container(:media_block_container)
    @css = css
  end

  def view_template
    fragment("fragment_media_wrapper") do
      div(id: dom_id, data: theme_data,  class: "@container #{@block_container_data.padding_y} #{@block_container_data.container} #{@block_presenter.media_alignment_class} #{parse_css}") do
        if block_given?
          yield
        else
          render media_component
        end
      end
    end
  end

  private

  def theme_data
    { theme: @block_container_data.theme }
  end

  def dom_id
    "block-#{@block_id}-media-layer"
  end

  def media_component
    if @block_presenter.media_options.type == "features"
      Ui::MediaComponents::Features.new(@block_presenter)
      elsif @block_presenter.media_options.type == "gallery"
      Ui::MediaComponents::Gallery.new(@block_presenter)
    end
  end
end
