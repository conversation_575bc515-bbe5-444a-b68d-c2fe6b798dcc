class Ui::OuterContainer < Phlex::HTML
  attr_reader :block_presenter, :outer_container_layer, :css, :style

  def initialize(block_presenter, css: nil, style: nil)
    @block_id = block_presenter.id
    @block_presenter = block_presenter
    @outer_container = block_presenter.container(:outer_block_container)
    @css = css
    @style = style
  end

  def view_template
    div(data: { theme: @outer_container.theme }, class: "relative bg-base-100", id: "block-#{@block_id}") do
      div(id: dom_id, data: { theme: @outer_container.theme }, class: "#{@outer_container.container}  mx-auto") do
        div(class: "#{container_classes} #{parse_css}", style: @style) do
          yield
        end
      end

      div(class: "absolute bottom-0 right-0 text-gray-400 z-50 text-xs my-0.5 mx-2") do
        admin_info
      end if admin?
    end
  end

  private

  def admin?
    @block_presenter.view_context.type == :admin
  end

  def admin_info
    "#{@block_id} • #{@block_presenter.name} • #{@outer_container.theme}"
  end

  def container_classes
    "overflow-hidden relative block #{@outer_container.padding_y}"
  end

  def dom_id
    "block-#{@block_id}-outer-container-layer"
  end
end
