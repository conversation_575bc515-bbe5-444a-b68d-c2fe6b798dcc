# frozen_string_literal: true

class Ui::ContentContainer < Phlex::HTML
  attr_reader :controls, :block_id, :content_layer, :css, :style

  def initialize(block_presenter,  css: nil, style: nil)
    @controls = block_presenter.controls
    @block_id =  block_presenter.id
    @block_container_data = block_presenter.container(:content_block_container)
    @css = css
    @style = style
  end

  def view_template
    div(
      id: dom_id,
      data: theme_data,
      class: "#{parse_css} #{@block_container_data.padding_y} #{@block_container_data.container} #{@block_container_data.padding_x} #{@block_container_data.alignment} #{@block_container_data.gap_y} #{static_css}"
    ) do
      yield if block_given?

      controls.each do |control|
        render control.component
      end
    end
  end

  private

  def theme_data
    { theme: @block_container_data.theme }
  end

  def static_css
    "mx-auto mobile-padding relative z-10 flex flex-col"
  end

  def dom_id
    "block-#{@block_id}-content-layer"
  end
end
