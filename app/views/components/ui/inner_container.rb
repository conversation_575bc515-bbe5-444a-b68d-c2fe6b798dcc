class Ui::InnerContainer < ApplicationComponent
  attr_reader :block_presenter, :block_id, :block_container_data, :css

  def initialize(block_presenter, css: nil)
    @block_presenter = block_presenter
    @block_id = block_presenter.id
    @block_container_data = block_presenter.container(:inner_block_container)

    @css = css
  end

  def view_template
    div(
      data_controller: "block",
      data: theme_data,
      id: dom_id,
      class: "#{parse_css} #{rounded_box_class} #{@block_container_data.gap} #{@block_container_data.container} #{@block_container_data.padding_y} mx-auto overflow-hidden relative"
    ) do
      yield
    end
  end

  private

  def theme_data
    { theme: @block_container_data.theme }
  end

  def rounded_box_class
    @block_container_data.padding_y == "sm:py-0" ? "rounded-none" : "rounded-box"
  end

  def dom_id
    "block-#{@block_id}-inner-container-layer"
  end
end
