<!DOCTYPE html>
<html>
<head>
  <title>Pozvánka do spr<PERSON>vy <PERSON></title>
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <%= csrf_meta_tags %>
  <%= csp_meta_tag %>
  <%= stylesheet_link_tag "tailwind", "data-turbo-track": "reload" %>
</head>

<body class="bg-gray-50">
  <div class="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
        Pozvánka do správy <PERSON>
      </h2>
      <p class="mt-2 text-center text-sm text-gray-600">
        <%= @invitation.account.name %>
      </p>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
      <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
        <% if @existing_user %>
          <!-- Uživatel již existuje -->
          <div class="text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
              <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h3 class="mt-2 text-lg font-medium text-gray-900">Pozvánka přijata!</h3>
            <p class="mt-1 text-sm text-gray-500">
              Byli jste úspěšně přidáni do správy účtu <strong><%= @invitation.account.name %></strong>.
            </p>
            <div class="mt-6">
              <%= link_to "Přihlásit se", new_session_path, class: "w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
            </div>
          </div>
        <% else %>
          <!-- Registrační formulář pro nového uživatele -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">Dokončete registraci</h3>
            <p class="text-sm text-gray-600 mb-4">
              Pro přijetí pozvánky do správy účtu <strong><%= @invitation.account.name %></strong> dokončete registraci.
            </p>
            <div class="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-6">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-3">
                  <p class="text-sm text-yellow-700">
                    Tato pozvánka vyprší <%= time_ago_in_words(7.days.from_now - (Time.current - @invitation.created_at)) %>.
                  </p>
                </div>
              </div>
            </div>

            <%= form_with model: @user, url: register_invitation_path(@invitation.token), class: "space-y-6" do |f| %>
              <div>
                <label for="user_email" class="block text-sm font-medium text-gray-700">
                  E-mail
                </label>
                <div class="mt-1">
                  <%= f.email_field :email, 
                      value: @invitation.email,
                      readonly: true,
                      class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 bg-gray-50 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" %>
                </div>
              </div>

              <div>
                <label for="user_name" class="block text-sm font-medium text-gray-700">
                  Celé jméno
                </label>
                <div class="mt-1">
                  <%= f.text_field :name, 
                      class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                      placeholder: "Jan Novák" %>
                  <% if @user.errors[:name].any? %>
                    <p class="mt-2 text-sm text-red-600">
                      <%= @user.errors[:name].first %>
                    </p>
                  <% end %>
                </div>
              </div>

              <div>
                <label for="user_password" class="block text-sm font-medium text-gray-700">
                  Heslo
                </label>
                <div class="mt-1">
                  <%= f.password_field :password, 
                      class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" %>
                  <% if @user.errors[:password].any? %>
                    <p class="mt-2 text-sm text-red-600">
                      <%= @user.errors[:password].first %>
                    </p>
                  <% end %>
                </div>
              </div>

              <div>
                <label for="user_password_confirmation" class="block text-sm font-medium text-gray-700">
                  Potvrzení hesla
                </label>
                <div class="mt-1">
                  <%= f.password_field :password_confirmation, 
                      class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" %>
                  <% if @user.errors[:password_confirmation].any? %>
                    <p class="mt-2 text-sm text-red-600">
                      <%= @user.errors[:password_confirmation].first %>
                    </p>
                  <% end %>
                </div>
              </div>

              <div>
                <%= f.submit "Dokončit registraci", class: "w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
              </div>
            <% end %>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</body>
</html>
