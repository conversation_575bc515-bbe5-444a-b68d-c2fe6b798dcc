<style id="styles">
    :root {
        <% if current_tenant&.theme&.any? %>
        <%= generate_theme_css_variables %>
        <% else %>
        <%= generate_color_variables_from_yaml(@colors) %>
        <% end %>

        --custom-radius-selector: <%= @theme[:radius][:selector] %>;
        --custom-radius-field: <%= @theme[:radius][:field] %>;
        --custom-radius-box: <%= @theme[:radius][:box] %>;
        --custom-size-selector: <%= @theme[:sizes][:selector] %>;
        --custom-size-field: <%= @theme[:sizes][:field] %>;
        --custom-options-border: <%= @theme[:options][:border] %>;
        --custom-options-depth: <%= @theme[:options][:depth] %>;
        --custom-options-noise: <%= @theme[:options][:noise] %>;
    }

    /* Aplikace uživatelských nastavení pro DaisyUI */
    <% if current_tenant&.theme&.any? %>
    body {
        font-family: var(--theme-font-family, <%= @template_settings&.dig(:font_family) || 'Inter, system-ui, sans-serif' %>);
    }

    .control-container h1,
    .control-container h2,
    .control-container h3,
    .control-container h4,
    .control-container h5,
    .control-container h6 {
        font-family: var(--theme-heading-font-family, var(--theme-font-family, <%= @template_settings&.dig(:heading_font_family) || @template_settings&.dig(:font_family) || 'Inter, system-ui, sans-serif' %>));
    }
    <% end %>
</style>
