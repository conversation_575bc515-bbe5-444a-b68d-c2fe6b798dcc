<%= form_with model: [:admin, :content, page.becomes(Page)], data: { controller: 'nested-form', nested_form_wrapper_selector_value: '.nested-form-wrapper' }, id: 'page-edit-form' do |f| %>
  <% if page.errors.any? %>
    <div class="bg-red-50 mt-3 text-red-700 p-4 rounded-sm">
      <h2 class="text-lg font-semibold"><%= pluralize(page.errors.count, "chyba") %> brání v uložení:</h2>
      <ul class="mt-2 list-disc list-inside">
        <% page.errors.full_messages.each do |message| %>
          <li class="text-sm"><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="mt-4">
    <div class="mt-1">
      <div>
        <div class="flex space-x-4 mt-4">
          <div class="relative <%= page.type == 'Content' || page.is_homepage? ? 'w-3/5' : 'w-full' %>">
            <div class="flex-1 focus:outline-none bg-white border border-gray-100 w-full">
              <div class="relative mx-auto px-4">
                <div class="py-4">

                  <div>
                    <% case page.type || 'Content' %>
                    <% when 'Content' %>
                      <%= render "admin/content/pages/types/content_form", f:, page: %>
                    <% when 'Link' %>
                      <%= render "admin/content/pages/types/link_form", f:, page: %>
                    <% when 'Anchor' %>
                      <%= render "admin/content/pages/types/anchor_form", f:, page: %>
                    <% else %>
                      <%= render "admin/content/pages/types/content_form", f:, page: %>
                    <% end %>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <% if page.type == 'Content' || page.is_homepage? %>
            <div class="w-2/5">
              <div class="w-full bg-white p-4 border border-gray-100">
                <h6 class="form-section font-medium">SEO</h6>

                <div class="mt-4 bg-white">
                  <div>
                    <label class="label">
                      Meta title
                    </label>
                    <div class="mt-1">
                      <%= f.text_field :meta_title, class: "input" %>
                    </div>
                  </div>
                </div>

                <div class="mt-4 bg-white">
                  <div>
                    <label class="label">
                      Meta description
                    </label>
                    <div class="mt-1">
                      <%= f.text_area :meta_description, class: "textarea" %>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <div class="py-3 px-4 bg-base-200/80 border-t-2 border-primary/50">
    <div class="mx-auto">
      <button type="submit" class="btn btn-primary">Uložit nastavení</button>
    </div>
  </div>
  </div>
<% end %>