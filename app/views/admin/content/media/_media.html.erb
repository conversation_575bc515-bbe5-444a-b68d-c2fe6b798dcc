<div class="flex flex-col" data-controller="reveal" data-reveal-hidden-class="hidden">
  <div class="flex justify-between items-center">
    <h3 class="text-lg font-medium">Média</h3>

    <div class="flex space-x-1.5">
      <button data-action="reveal#toggle" type="button" class="btn btn-xs btn-soft cursor-pointer px-1">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
          <path fill-rule="evenodd" d="M6.455 1.45A.5.5 0 0 1 6.952 1h2.096a.5.5 0 0 1 .497.45l.186 1.858a4.996 4.996 0 0 1 1.466.848l1.703-.769a.5.5 0 0 1 .639.206l1.047 1.814a.5.5 0 0 1-.14.656l-1.517 1.09a5.026 5.026 0 0 1 0 1.694l1.516 1.09a.5.5 0 0 1 .141.656l-1.047 1.814a.5.5 0 0 1-.639.206l-1.703-.768c-.433.36-.928.649-1.466.847l-.186 1.858a.5.5 0 0 1-.497.45H6.952a.5.5 0 0 1-.497-.45l-.186-1.858a4.993 4.993 0 0 1-1.466-.848l-1.703.769a.5.5 0 0 1-.639-.206l-1.047-1.814a.5.5 0 0 1 .14-.656l1.517-1.09a5.033 5.033 0 0 1 0-1.694l-1.516-1.09a.5.5 0 0 1-.141-.656L2.46 3.593a.5.5 0 0 1 .639-.206l1.703.769c.433-.36.928-.65 1.466-.848l.186-1.858Zm-.177 7.567-.022-.037a2 2 0 0 1 3.466-1.997l.022.037a2 2 0 0 1-3.466 1.997Z" clip-rule="evenodd" />
        </svg>
      </button>


    <%= link_to new_admin_content_medium_path(block_id: block.id, page_id: @owner.id, media_type_id: block.media_type.id), class: "btn btn-xs btn-neutral cursor-pointer px-1", data: { controller: "tippy", tippy_content: "Přidat", turbo_frame: "media_sidebar" } do %>
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
        <path d="M8.75 3.75a.75.75 0 0 0-1.5 0v3.5h-3.5a.75.75 0 0 0 0 1.5h3.5v3.5a.75.75 0 0 0 1.5 0v-3.5h3.5a.75.75 0 0 0 0-1.5h-3.5v-3.5Z" />
      </svg>
    <% end %>
    </div>
  </div>

  <div data-reveal-target="item" class="hidden bg-base-100 border border-base-300 px-4 py-4 rounded-box mt-4">
    <label class="label label-sm font-normal">Nastavení</label>
    <%= form_with model: [@owner, @block], url: update_media_admin_content_page_block_path(@block, page_id: @owner), method: :patch, id: "media-block-form", data: { controller: "form" }, class: "flex flex-col h-full" do |form| %>
      <div>
        <label class="text-xs">Typ</label>
        <%= form.select :media_type_id, MediaType.all.map { |type| [type.name, type.id] }, {}, { class: "select", onchange: "this.form.requestSubmit()" } %>
      </div>

      <div>
        <label class="text-xs">Kolekce</label>
        <%= form.combobox :media_collection_id, MediaCollection.with_media,
                          data: { action: "hw-combobox:selection->form#requestSubmit" }, render_in: { partial: "admin/content/page_blocks/media_controls/hw_combobox_search_image" } %>
      </div>
  <% end %>
  </div>

  <% if block.media.any? %>
    <div class="bg-base-100 border border-base-300 px-4 py-4 rounded-box mt-4">
      <label class="label label-sm font-normal">Obsah</label>
      <%= render "admin/content/media/media_items", block: block, page: @page %>
    </div>
  <% else %>

    <div class="mt-4 p-4 bg-gray-50 rounded border border-gray-200 text-center text-gray-500">
      Zatím nejsou přidána žádná média. Klikněte na tlačítko "Přidat" pro přidání nového média.
    </div>
  <% end %>
</div>