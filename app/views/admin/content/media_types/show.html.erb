<div class="container mx-auto py-6">
  <div class="flex justify-between items-center mb-6">
    <div>
      <h1 class="text-2xl font-bold"><%= @media_type.name %></h1>
      <p class="text-gray-500">Typ média</p>
    </div>
  </div>
  
  <div class="bg-white shadow overflow-hidden rounded-lg mb-6">
    <div class="px-6 py-4">
      <h2 class="text-lg font-medium text-gray-900 mb-2">Detaily typu média</h2>
      <div class="grid grid-cols-2 gap-4">
        <div>
          <p class="text-sm font-medium text-gray-500">Název</p>
          <p class="mt-1 text-sm text-gray-900"><%= @media_type.name %></p>
        </div>

        <div>
          <p class="text-sm font-medium text-gray-500">Počet polí</p>
          <p class="mt-1 text-sm text-gray-900"><%= @media_type.media_fields.count %></p>
        </div>
        <div>
          <p class="text-sm font-medium text-gray-500">Počet médií</p>
          <p class="mt-1 text-sm text-gray-900"><%= @media_type.media.count %></p>
        </div>
      </div>
    </div>
  </div>
</div>


  <div class="mt-8">
    <h2 class="text-xl font-semibold text-gray-900 mb-4">Kolekce</h2>
    <% if @media_type.collections.any? %>
      <% @media_type.collections.each do |collection| %>
        <div class="bg-white shadow overflow-hidden rounded-lg mb-6">
          <div class="px-6 py-4">
            <h3 class="text-lg font-medium text-gray-900 mb-2"><%= collection.name %></h3>
            <% if collection.media.any? %>
              <ul class="grid grid-cols-3 sm:grid-cols-8 divide-y divide-gray-200">
                <% collection.media.each do |media_item| %>
                  <li class="py-3 flex flex-col justify-between items-center">
                    <% if media_item.image.attached? %>
                      <%= image_tag media_item.image.variant(:thumb), class: "rounded-lg w-32 h-32" %>
                    <% end %>

                    <p class="w-32 mt-1 text-xs text-center truncate">
                      <%= link_to media_item.try(:title) || "Nepojmenované médium",
                                  [:admin, :content, media_item],
                                  class: "text-blue-600 hover:text-blue-800 hover:underline" %>
                    </p>
                  </li>
                <% end %>
              </ul>
            <% else %>
              <p class="text-sm text-gray-500">Tato kolekce neobsahuje žádná média.</p>
            <% end %>
          </div>
        </div>
      <% end %>
    <% else %>
      <div class="bg-white shadow overflow-hidden rounded-lg">
        <div class="px-6 py-4">
          <p class="text-sm text-gray-500">Pro tento typ média nebyly nalezeny žádné kolekce.</p>
        </div>
      </div>
    <% end %>
  </div>
