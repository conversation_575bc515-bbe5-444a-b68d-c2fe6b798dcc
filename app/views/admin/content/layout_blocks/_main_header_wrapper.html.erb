<div id="edit-block-container-<%= block.id %>" data-content-theme="<%= block.content_theme %>" class="relative w-full block-container group">
  <div class="block-toolbar hidden group-hover:flex absolute top-0 left-0 z-40">
    <div class="flex bg-gray-800 rounded-lg ml-3 mt-2.5">
      <%= link_to edit_polymorphic_path([:admin, :content, owner, block]), class: "rounded-l-lg flex items-center px-2 py-1.5 hover:bg-black", data: { turbo_frame: "sidebar", action: "click->blocks#save" } do %>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="text-white h-4 w-4">
          <path fill-rule="evenodd" d="M6.955 1.45A.5.5 0 0 1 7.452 1h1.096a.5.5 0 0 1 .497.45l.17 1.699c.484.12.94.312 1.356.562l1.321-1.081a.5.5 0 0 1 .67.033l.774.775a.5.5 0 0 1 .034.67l-1.08 1.32c.25.417.44.873.561 1.357l1.699.17a.5.5 0 0 1 .45.497v1.096a.5.5 0 0 1-.45.497l-1.699.17c-.12.484-.312.94-.562 1.356l1.082 1.322a.5.5 0 0 1-.034.67l-.774.774a.5.5 0 0 1-.67.033l-1.322-1.08c-.416.25-.872.44-1.356.561l-.17 1.699a.5.5 0 0 1-.497.45H7.452a.5.5 0 0 1-.497-.45l-.17-1.699a4.973 4.973 0 0 1-1.356-.562L4.108 13.37a.5.5 0 0 1-.67-.033l-.774-.775a.5.5 0 0 1-.034-.67l1.08-1.32a4.971 4.971 0 0 1-.561-1.357l-1.699-.17A.5.5 0 0 1 1 8.548V7.452a.5.5 0 0 1 .45-.497l1.699-.17c.12-.484.312-.94.562-1.356L2.629 4.107a.5.5 0 0 1 .034-.67l.774-.774a.5.5 0 0 1 .67-.033L5.43 3.71a4.97 4.97 0 0 1 1.356-.561l.17-1.699ZM6 8c0 .538.212 1.026.558 1.385l.057.057a2 2 0 0 0 2.828-2.828l-.058-.056A2 2 0 0 0 6 8Z" clip-rule="evenodd" />
        </svg>
      <% end %>


      <%= link_to new_admin_content_layout_block_path(@layout, add: 'header', location: 'main_header'), data: { turbo_frame: "sidebar" }, class: "rounded-r-lg flex items-center px-2 py-1.5 hover:bg-black" do %>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="text-white h-4 w-4">
          <path fill-rule="evenodd" d="M15.312 11.424a5.5 5.5 0 0 1-9.201 2.466l-.312-.311h2.433a.75.75 0 0 0 0-1.5H3.989a.75.75 0 0 0-.75.75v4.242a.75.75 0 0 0 1.5 0v-2.43l.31.31a7 7 0 0 0 11.712-*********** 0 0 0-1.449-.39Zm1.23-3.723a.75.75 0 0 0 .219-.53V2.929a.75.75 0 0 0-1.5 0V5.36l-.31-.31A7 7 0 0 0 3.239 8.188a.75.75 0 1 0 1.448.389A5.5 5.5 0 0 1 13.89 6.11l.311.31h-2.432a.75.75 0 0 0 0 1.5h4.243a.75.75 0 0 0 .53-.219Z" clip-rule="evenodd" />
        </svg>
    <% end %>
    </div>
  </div>

  <%= render component %>

  <div class="block-toolbar hidden group-hover:flex absolute w-full items-center justify-center -mt-4 z-50">
    <a href="<%= new_admin_content_layout_block_path(@layout, location: 'header') %>" data-turbo-frame="sidebar" class="w-8 h-8 bg-black hover:bg-gray-800 text-white flex items-center justify-center rounded-full">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="size-5">
        <path d="M10.75 4.75a.75.75 0 0 0-1.5 0v4.5h-4.5a.75.75 0 0 0 0 1.5h4.5v4.5a.75.75 0 0 0 1.5 0v-4.5h4.5a.75.75 0 0 0 0-1.5h-4.5v-4.5Z" />
      </svg>
    </a>
  </div>

  <div class="block-overlay hidden group-hover:block absolute top-0 left-0 w-full h-full opacity-10 z-20 bg-black"></div>
  <div class="block-overlay-lg hidden absolute top-0 left-0 w-full h-full opacity-80 bg-black z-50"></div>
</div>
