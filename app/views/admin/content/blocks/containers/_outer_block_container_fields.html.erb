<%= f.hidden_field :type %>
<fieldset class="fieldset bg-base-100 border border-base-300 px-4 pb-3 rounded-box">
  <legend class="fieldset-legend"><span class="w-3 h-3 bg-purple-500 rounded-full"></span> V<PERSON><PERSON><PERSON><PERSON><PERSON></legend>

  <div class="flex flex-col space-y-2" data-controller="block" data-block-name-value="outer-container-layer" data-block-container-value="block-<%= block.id %>-outer-container-layer" data-block-theme="<%= f.object.theme %>">
    <div class="flex justify-between items-center">
      <label class="fieldset-label text-gray-700 w-1/2">Téma</label>
      <div class="flex relative"
           data-controller="dropdown"
           data-action="click@window->dropdown#hide touchstart@window->dropdown#hide keydown.up->dropdown#previousItem keydown.down->dropdown#nextItem keydown.esc->dropdown#hide"
      >
        <button type="button" data-action="dropdown#toggle:stop" data-dropdown-target="button" class="flex items-center space-x-0.5 bg-white text-black text-sm py-1 px-1.5 rounded-md border border-gray-200">
          <span class="flex">
            <% @colors.each do |key, options| %>
              <div data-theme="<%= key %>" class="hidden selected-<%= key %> <%= options[:background] %> bg-base-100 w-4 h-4 border border-gray-400 rounded-full"></div>
            <% end %>
          </span>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
            <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
          </svg>
        </button>
        <div data-dropdown-target="menu" class="hidden absolute top-4 right-0 z-50 mt-5 flex w-screen max-w-max p-3 bg-white rounded-md border border-gray-400">
          <div class="text-sm w-44">
            <label data-theme="auto" class="justify-between no-underline flex px-3 py-2 rounded text-gray-900 bg-white hover:bg-gray-100">
              <div class="flex space-x-1.5">
                <div class="flex">
                  <div class="auto bg-base-100 w-5 h-5 border border-gray-400 rounded-full"></div>
                </div>
                <span>Auto</span>
              </div>
              <%= f.radio_button :theme, :auto, { 'data-action': 'dropdown#change dropdown#toggle block#onChangeTheme', 'data-dropdown-target': 'input' } %>
            </label>
            <% @colors.each do |key, options| %>
              <label data-theme="<%= key %>" class="justify-between no-underline flex px-3 py-2 rounded text-gray-900 bg-white hover:bg-gray-100">
                <div class="flex space-x-1.5">
                  <div class="flex">
                    <div class="<%= key %> bg-base-100 w-5 h-5 border border-gray-400 rounded-full"></div>
                  </div>
                  <span><%= options[:name] %></span>
                </div>
                <%= f.radio_button :theme, key, { 'data-action': 'dropdown#change dropdown#toggle block#onChangeTheme', 'data-dropdown-target': 'input' } %>
              </label>
            <% end %>
          </div>
        </div>
      </div>
    </div>
    <div class="w-full flex justify-between items-center space-x-3">
      <label class="fieldset-label text-gray-700 w-1/2">Překrytí</label>
      <div class="flex-1" data-controller="slider" data-slider-element-value="block-<%= block.id %>-outer-container-layer-overlay" data-slider-current-value="opacity-<%= f.object.background_overlay_opacity || 0 %>" data-slider-values-value='["opacity-0", "opacity-10", "opacity-20", "opacity-30", "opacity-40", "opacity-50", "opacity-60", "opacity-70", "opacity-80", "opacity-90", "opacity-100"]'>
        <div data-slider-target="handler" class="slider-styled slider-round"></div>
        <%= f.hidden_field :background_overlay_opacity, min: 0, max: 10, data: { 'slider-target': "input" }, class: "w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700" %>
      </div>
    </div>
    <div class="w-full flex justify-between items-center space-x-3">
      <label class="fieldset-label text-gray-700 w-1/2">Velikost</label>
      <div class="flex-1" data-controller="slider" data-slider-element-value="block-<%= block.id %>-outer-container-layer" data-slider-current-value="<%= f.object.container %>" data-slider-values-value='["max-w-xl", "max-w-2xl", "max-w-3xl", "max-w-4xl", "max-w-5xl", "max-w-6xl", "w-full"]'>
        <div data-slider-target="handler" class="slider-styled slider-round"></div>
        <%= f.hidden_field :container, value: f.object.container, min: 0, max: 6, data: { 'slider-target': "input" }, class: "w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700" %>
      </div>
    </div>
    <div class="w-full flex justify-between items-center space-x-3">
      <label class="fieldset-label text-gray-700 w-1/2">Odsazení - Y</label>
      <div class="flex-1" data-controller="slider" data-slider-element-value="block-<%= block.id %>-outer-container-layer" data-slider-current-value="<%= f.object.padding_y %>" data-slider-values-value='["sm:py-0", "sm:py-0.5", "sm:py-1", "sm:py-2", "sm:py-4", "sm:py-6", "sm:py-8", "sm:py-10", "sm:py-12", "sm:py-16", "sm:py-20", "sm:py-24", "sm:py-32", "sm:py-64"]'>
        <div data-slider-target="handler" class="slider-styled slider-round"></div>
        <%= f.hidden_field :padding_y, value: f.object.padding_y, min: 0, max: 12, data: { 'slider-target': "input" },  class: "w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700" %>
      </div>
    </div>
  </div>
</fieldset>