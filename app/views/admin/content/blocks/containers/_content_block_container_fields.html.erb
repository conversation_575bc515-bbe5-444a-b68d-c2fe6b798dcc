<%= f.hidden_field :type %>
<fieldset class="fieldset bg-base-100 border border-base-300 px-4 pb-3 rounded-box">
  <legend class="fieldset-legend"><span class="w-3 h-3 bg-purple-500 rounded-full"></span> <PERSON><PERSON><PERSON><PERSON><PERSON></legend>

  <div class="flex flex-col space-y-2" data-controller="block" data-block-name-value="content-layer" data-block-alignment-value="<%= f.object.alignment %>" data-block-container-value="block-<%= block.id %>-content-layer" data-block-theme="<%= f.object.theme %>">
    <div class="flex justify-between items-center">
      <label class="fieldset-label text-gray-700 w-1/2">Téma</label>
      <div class="flex relative"
           data-controller="dropdown"
           data-action="click@window->dropdown#hide touchstart@window->dropdown#hide keydown.up->dropdown#previousItem keydown.down->dropdown#nextItem keydown.esc->dropdown#hide"
      >
        <button type="button" data-action="dropdown#toggle:stop" data-dropdown-target="button" class="flex items-center space-x-0.5 bg-white text-black text-sm py-1 px-1.5 rounded-md border border-gray-200">
          <span class="flex">
            <% @colors.each do |key, options| %>
              <div data-theme="<%= key %>" class="hidden selected-<%= key %> <%= options[:background] %> bg-base-100 w-4 h-4 border border-gray-400 rounded-full"></div>
            <% end %>
          </span>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
            <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
          </svg>
        </button>
        <div data-dropdown-target="menu" class="hidden absolute top-4 right-0 z-50 mt-5 flex w-screen max-w-max p-3 bg-white rounded-md border border-gray-400">
          <div class="text-sm w-44">
            <label data-theme="auto" class="justify-between no-underline flex px-3 py-2 rounded text-gray-900 bg-white hover:bg-gray-100">
              <div class="flex space-x-1.5">
                <div class="flex">
                  <div class="auto bg-base-100 w-5 h-5 border border-gray-400 rounded-full"></div>
                </div>
                <span>Auto</span>
              </div>
              <%= f.radio_button :theme, :auto, { 'data-action': 'dropdown#change dropdown#toggle block#onChangeTheme', 'data-dropdown-target': 'input' } %>
            </label>
            <% @colors.each do |key, options| %>
              <label data-theme="<%= key %>" class="justify-between no-underline flex px-3 py-2 rounded text-gray-900 bg-white hover:bg-gray-100">
                <div class="flex space-x-1.5">
                  <div class="flex">
                    <div class="<%= key %> bg-base-100 w-5 h-5 border border-gray-400 rounded-full"></div>
                  </div>
                  <span><%= options[:name] %></span>
                </div>
                <%= f.radio_button :theme, key, { 'data-action': 'dropdown#change dropdown#toggle block#onChangeTheme', 'data-dropdown-target': 'input' } %>
              </label>
            <% end %>
          </div>
        </div>
      </div>
    </div>
    <div class="w-full flex justify-between items-center space-x-3">
      <label class="fieldset-label text-gray-700 w-1/2">Velikost</label>
      <div class="flex-1" data-controller="slider" data-slider-element-value="block-<%= block.id %>-content-layer" data-slider-current-value="<%= f.object.container %>" data-slider-values-value='["container-half", "container-two-thirds", "container-three-quarters", "container-wide", "container-very-wide", "w-full"]'>
        <div data-slider-target="handler" class="slider-styled slider-round"></div>
        <%= f.hidden_field :container, value: f.object.container, min: 1, max: 6, data: { 'slider-target': "input" }, class: "w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700" %>
      </div>
    </div>
    <div class="w-full flex justify-between items-center space-x-3">
      <label class="fieldset-label text-gray-700 w-1/2">Odsazení - Y</label>
      <div class="flex-1" data-controller="slider" data-slider-element-value="block-<%= block.id %>-content-layer" data-slider-current-value="<%= f.object.padding_y %>" data-slider-values-value='["sm:py-0", "sm:py-0.5", "sm:py-1", "sm:py-2", "sm:py-4", "sm:py-6", "sm:py-8", "sm:py-10", "sm:py-12", "sm:py-16", "sm:py-20", "sm:py-24", "sm:py-32", "sm:py-64"]'>
        <div data-slider-target="handler" class="slider-styled slider-round"></div>
        <%= f.hidden_field :padding_y, value: f.object.padding_y, min: 0, max: 12, data: { 'slider-target': "input" },  class: "w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700" %>
      </div>
    </div>
    <div class="w-full flex justify-between items-center space-x-3">
      <label class="fieldset-label text-gray-700 w-1/2">Odsazení - X</label>
      <div class="flex-1" data-controller="slider" data-slider-element-value="block-<%= block.id %>-content-layer" data-slider-current-value="<%= f.object.padding_x %>" data-slider-values-value='["sm:px-0", "sm:px-0.5", "sm:px-1", "sm:px-2", "sm:px-4", "sm:px-6", "sm:px-8", "sm:px-10", "sm:px-12", "sm:px-16", "sm:px-20", "sm:px-24", "sm:px-32", "sm:px-64"]'>
        <div data-slider-target="handler" class="slider-styled slider-round"></div>
        <%= f.hidden_field :padding_x, value: f.object.padding_x, min: 0, max: 12, data: { 'slider-target': "input" },  class: "w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700" %>
      </div>
    </div>
    <div class="w-full flex justify-between items-center space-x-3">
      <label class="fieldset-label text-gray-700 w-1/2">Odsazení obsahu</label>
      <div class="flex-1" data-controller="slider" data-slider-element-value="block-<%= block.id %>-content-layer" data-slider-current-value="<%= f.object.gap_y %>" data-slider-values-value='["gap-y-0", "gap-y-0.5", "gap-y-1", "gap-y-1.5", "gap-y-2", "gap-y-2.5", "gap-y-3", "gap-y-3.5", "gap-y-4", "gap-y-4.5", "gap-y-5", "gap-y-5.5", "gap-y-6", "gap-y-8", "gap-y-10", "gap-y-12", "gap-y-16", "gap-y-20", "gap-y-24"]'>
        <div data-slider-target="handler" class="slider-styled slider-round"></div>
        <%= f.hidden_field :gap_y, value: f.object.gap_y, min: 0, max: 16, data: { 'slider-target': "input" },  class: "w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700" %>
      </div>
    </div>
    <div class="w-full flex justify-between items-center space-x-3">
      <label class="fieldset-label text-gray-700 w-1/2">Zarovnání</label>
      <fieldset>
        <div class="grid grid-cols-3 gap-3 sm:grid-cols-3">
          <% %w[alignment-left alignment-center alignment-right].each do |align| %>
            <%= f.radio_button :alignment, align, class: "peer/#{align} hidden", 'data-action': 'block#onChangeAlignment', 'data-dropdown-target': 'input', id: "alignment-#{align}" %>
            <label for="alignment-<%= align %>" class="flex cursor-pointer items-center justify-center border border-gray-100 rounded-md p-1.5 text-sm font-semibold uppercase focus:outline-hidden sm:flex-1 bg-white peer-checked/<%= align %>:bg-black peer-checked/<%= align %>:text-white">
              <span>
                <% if align == "alignment-left" %>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
                    <path fill-rule="evenodd" d="M2 3.75A.75.75 0 0 1 2.75 3h10.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 3.75ZM2 8a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 8Zm0 4.25a.75.75 0 0 1 .75-.75h4.5a.75.75 0 0 1 0 1.5h-4.5a.75.75 0 0 1-.75-.75Z" clip-rule="evenodd" />
                  </svg>
                <% elsif align == "alignment-center" %>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
                    <path fill-rule="evenodd" d="M2 3.75A.75.75 0 0 1 2.75 3h10.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 3.75ZM2 8a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 8Zm0 4.25a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H2.75a.75.75 0 0 1-.75-.75Z" clip-rule="evenodd" />
                  </svg>
                <% elsif align == "alignment-right" %>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
                    <path fill-rule="evenodd" d="M2 3.75A.75.75 0 0 1 2.75 3h10.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 3.75ZM2 8a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 8Zm6 4.25a.75.75 0 0 1 .75-.75h4.5a.75.75 0 0 1 0 1.5h-4.5a.75.75 0 0 1-.75-.75Z" clip-rule="evenodd" />
                  </svg>
                <% end %>
              </span>
            </label>
          <% end %>
        </div>
      </fieldset>
    </div>
  </div>
</fieldset>