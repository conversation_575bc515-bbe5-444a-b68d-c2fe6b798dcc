<%= form_with model: [@page, @block], url: admin_content_medium_path(@block), method: :patch, id: "media-block-form", class: "flex flex-col h-full" do |form| %>
<div class="">
  <h3 class="font-medium mb-1">Fotogalerie</h3>

  <div class="flex flex-col space-y-2">
  <% if GalleryMediaCollection.any? %>
    <div>
      <label class="text-xs">Zdroj fotogalerie</label>
      <%= form.combobox :media_collection_id, GalleryMediaCollection.with_media,
                        data: { action: "hw-combobox:selection->form#requestSubmit" }, render_in: { partial: "admin/content/blocks/media_controls/hw_combobox_search_image" } %>
    </div>
  <% end %>

  <% if @block.media_position.present? %>
      <div class="flex space-x-2">
        <div class="w-full">
          <label class="text-xs">Po<PERSON>ce</label>
          <%= form.select :media_position, [["V pravo", "right"], ["V levo", "left"]], {}, { class: "select", onchange: "this.form.requestSubmit()" } %>
        </div>
      </div>
  <% end %>
  </div>
  <div data-controller="uploads" data-uploads-response-kind-value="turbo-stream" data-uploads-block-id-value="<%= block.id %>" data-uploads-url-value="<%= admin_content_media_path(@website, @block.id) %>" class="flex flex-col space-y-2 mt-4" id="gallery-upload">
    <div class="flex items-center justify-center w-full">
      <label for="dropzone-file" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-gray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500 dark:hover:bg-gray-600">
        <div class="flex flex-col items-center justify-center pt-5 pb-6">
          <svg class="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
          </svg>
          <p class="mb-2 text-sm text-gray-500 dark:text-gray-400 text-center"><span class="font-semibold">Klikněte pro nahrání</span></p>
          <p class="text-xs text-gray-500 dark:text-gray-400">nebo přetáhněte soubory sem</p>
        </div>
        <input id="dropzone-file" type="file" class="hidden" data-action="change->uploads#upload" accept="image/*" data-uploads-target="fileInput" multiple>
      </label>
    </div>

    <%= render "/admin/content/blocks/media_controls/gallery_images", block: @block %>
  </div>
</div>
<% end %>