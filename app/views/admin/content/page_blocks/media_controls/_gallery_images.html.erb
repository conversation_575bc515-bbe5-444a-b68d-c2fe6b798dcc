<ul class="mt-4 grid grid-cols-5 gap-2" id="media-items" data-controller="sortable">
  <% block.media.each do |media| %>
    <li class="relative group" id="<%= dom_id(media) %>" data-sortable-update-url="<%= admin_content_medium_path(media) %>">
      <%= image_tag media.image.variant(resize_to_fill: [170, 170]) %>

      <div class="flex justify-center">
        <div class="hidden group-hover:block absolute top-0 left-0 bg-black opacity-50 w-full h-full"></div>
        <%= link_to admin_content_medium_path(media, block_id: block.id), class: "hidden group-hover:flex bg-black-50 absolute top-0 left-0 w-full h-full justify-center items-center", data: { turbo_method: :delete } do %>
          <span class="bg-red-500 text-white rounded p-1">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
              <path d="M5.28 4.22a.75.75 0 0 0-1.06 1.06L6.94 8l-2.72 2.72a.75.75 0 1 0 1.06 1.06L8 9.06l2.72 2.72a.75.75 0 1 0 1.06-1.06L9.06 8l2.72-2.72a.75.75 0 0 0-1.06-1.06L8 6.94 5.28 4.22Z" />
            </svg>
          </span>
        <% end %>
      </div>
    </li>
  <% end %>
</ul>