
<div class="bg-base-100 border border-base-300 px-4 py-4 rounded-box">
  <label class="label label-sm font-normal">Text</label>

  <%= b.hidden_field :content, value: b.object.content.to_json, id: "tiptap_json_input_#{b.object.id}" %>
  <%= b.hidden_field :text, id: "tiptap_html_input_#{b.object.id}" %>

  <div
    data-controller="tiptap"
    data-editor-type="bubble"
    data-initial-content="<%= b.object.text.to_json %>"
    data-html-input-id="tiptap_html_input_<%= b.object.id %>"
    data-json-input-id="tiptap_json_input_<%= b.object.id %>"
    data-preview-element-id="<%= "control-#{b.object.id}" %>"
    data-block-theme="<%= block.content_theme %>"
    data-editor-class-name="textarea text-black bg-white h-auto rounded border border-gray-300 p-2"
  >
  </div>
</div>