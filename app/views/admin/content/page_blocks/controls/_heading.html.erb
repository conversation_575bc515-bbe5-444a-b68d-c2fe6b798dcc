<div class="bg-base-100 border border-base-300 px-4 py-4 rounded-box">
  <label class="label label-sm font-normal">Nadpis sekce</label>

  <%= b.hidden_field :content, value: b.object.content.to_json, id: "tiptap_json_input_#{b.object.id}" %>
  <%= b.hidden_field :text, id: "tiptap_html_input_#{b.object.id}" %>

  <div
    data-controller="tiptap"
    data-editor-type="bubble"
    data-initial-content="<%= b.object.text.to_json %>"
    data-html-input-id="tiptap_html_input_<%= b.object.id %>"
    data-json-input-id="tiptap_json_input_<%= b.object.id %>"
    data-preview-element-id="<%= "control-#{b.object.id}-heading" %>"
    data-editor-class-name="input text-black bg-white h-auto rounded border border-gray-300 p-2"
    data-block-theme="<%= block.content_theme %>"
  >
  </div>

  <div class="mt-3">
    <%= b.text_field :pre_header, class: "input",  data: { pre_header_text_value: b.object.pre_header, action: 'heading#onUpdatePreHeader' }, placeholder: "Předtitulek" %>
  </div>
</div>