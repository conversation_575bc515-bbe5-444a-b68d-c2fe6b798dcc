<div class="bg-base-100 border border-base-300 px-4 py-4 rounded-box">
  <div class="buttons-control hw-combobox-in-group" data-controller="buttons" data-buttons-control-id-value="<%= b.object.id %>">
    <div class="flex justify-between items-center">
      <h3 class="font-medium mb-1">CTA</h3>
    </div>

    <div>
      <h4 class="text-sm mb-1">Primární</h4>
      <div class="flex flex-col space-y-2">
        <%= b.text_field :primary_button_text, placeholder: "Text tlačítka", data: { action: 'buttons#onUpdatePrimaryButtonText' }, class: "input" %>
        <div>
          <div class="rounded-md flex relative"
               data-controller="dropdown"
               data-action="click@window->dropdown#hide touchstart@window->dropdown#hide keydown.up->dropdown#previousItem keydown.down->dropdown#nextItem keydown.esc->dropdown#hide"
          >
            <button type="button" data-action="dropdown#toggle:stop"  data-dropdown-target="button" class="flex items-center space-x-0.5 bg-white text-black text-sm p-2 rounded-l-md border-l border-gray-300 border-y">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="hidden selected-link size-5">
                <path stroke-linecap="round" stroke-linejoin="round" d="M13.19 8.688a4.5 4.5 0 0 1 1.242 7.244l-4.5 4.5a4.5 4.5 0 0 1-6.364-6.364l1.757-1.757m13.35-.622 1.757-1.757a4.5 4.5 0 0 0-6.364-6.364l-4.5 4.5a4.5 4.5 0 0 0 1.242 7.244" />
              </svg>
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="hidden selected-page size-5">
                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
              </svg>
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="hidden selected-email size-5">
                <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75" />
              </svg>
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="hidden selected-phone size-5">
                <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 1.5H8.25A2.25 2.25 0 0 0 6 3.75v16.5a2.25 2.25 0 0 0 2.25 2.25h7.5A2.25 2.25 0 0 0 18 20.25V3.75a2.25 2.25 0 0 0-2.25-2.25H13.5m-3 0V3h3V1.5m-3 0h3m-3 18.75h3" />
              </svg>

              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
                <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
              </svg>
            </button>
            <div data-dropdown-target="menu" class="hidden absolute top-4 left-0 z-10 mt-5 flex w-screen max-w-max p-3 bg-white rounded border">
              <div class="text-sm overflow-hidden w-44">
                <label class='justify-between no-underline flex items-center px-2 py-2 rounded text-gray-900 bg-white hover:bg-gray-100'>
                  <div class="flex space-x-1.5">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-5">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
                    </svg>
                    <span>Stránka</span>
                  </div>

                  <%= b.radio_button :primary_button_link_type, "page", { 'data-action': 'dropdown#change dropdown#toggle', 'data-dropdown-target': 'input', class: "radio radio-xs" } %>
                </label>
                <label class='justify-between no-underline flex items-center px-2 py-2 rounded text-gray-900 bg-white hover:bg-gray-100'>
                  <div class="flex space-x-1.5">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-5">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M13.19 8.688a4.5 4.5 0 0 1 1.242 7.244l-4.5 4.5a4.5 4.5 0 0 1-6.364-6.364l1.757-1.757m13.35-.622 1.757-1.757a4.5 4.5 0 0 0-6.364-6.364l-4.5 4.5a4.5 4.5 0 0 0 1.242 7.244" />
                    </svg>
                    <span>URL</span>
                  </div>
                  <%= b.radio_button :primary_button_link_type, "link", { 'data-action': 'dropdown#change dropdown#toggle', 'data-dropdown-target': 'input', class: "radio radio-xs" } %>
                </label>
                <label class='justify-between no-underline flex items-center px-2 py-2 rounded text-gray-900 bg-white hover:bg-gray-100'>
                  <div class="flex space-x-1.5">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-5">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75" />
                    </svg>
                    <span>E-mail</span>
                  </div>
                  <%= b.radio_button :primary_button_link_type, "email", { 'data-action': 'dropdown#change dropdown#toggle', 'data-dropdown-target': 'input', class: "radio radio-xs" } %>
                </label>
                <label class='justify-between no-underline flex items-center px-2 py-2 rounded text-gray-900 bg-white hover:bg-gray-100'>
                  <div class="flex space-x-1.5">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-5">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 1.5H8.25A2.25 2.25 0 0 0 6 3.75v16.5a2.25 2.25 0 0 0 2.25 2.25h7.5A2.25 2.25 0 0 0 18 20.25V3.75a2.25 2.25 0 0 0-2.25-2.25H13.5m-3 0V3h3V1.5m-3 0h3m-3 18.75h3" />
                    </svg>

                    <span>Telefon</span>
                  </div>
                  <%= b.radio_button :primary_button_link_type, "phone", { 'data-action': 'dropdown#change dropdown#toggle', 'data-dropdown-target': 'input', class: "radio radio-xs" } %>
                </label>
                <div class="border-t pt-2 mt-2">
                  <label class="flex space-x-1.5 items-center no-underline px-2 py-2 rounded text-gray-900 bg-white hover:bg-gray-100">
                    <input type="checkbox"> <span>V novém okně</span>
                  </label>
                </div>
              </div>
            </div>

            <div class="selected-page hidden w-full">
              <%= b.combobox :primary_button_page_id, Page.all, value: b.object.primary_button_page_id.to_i, placeholder: "Vyberte stránku" %>
            </div>

            <div class="selected-link hidden w-full">
              <%= b.text_field :primary_button_link, placeholder: "Odkaz", class: "w-full input rounded-none rounded-r" %>
            </div>

            <div class="selected-email hidden w-full">
              <%= b.email_field :primary_button_email, placeholder: "E-mail", class: "w-full input rounded-none rounded-r" %>
            </div>

            <div class="selected-phone hidden w-full">
              <%= b.telephone_field :primary_button_phone, placeholder: "Telefon", class: "w-full input rounded-none rounded-r" %>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="mt-2">
      <h4 class="text-sm mb-1">Sekundární</h4>
      <div class="flex flex-col space-y-2">
        <%= b.text_field :secondary_button_text, placeholder: "Text tlačítka", data: { action: 'buttons#onUpdateSecondaryButtonText' }, class: "input" %>

        <div>
          <div class="rounded-md flex relative"
               data-controller="dropdown"
               data-action="click@window->dropdown#hide touchstart@window->dropdown#hide keydown.up->dropdown#previousItem keydown.down->dropdown#nextItem keydown.esc->dropdown#hide"
          >
            <button type="button" data-action="dropdown#toggle:stop"  data-dropdown-target="button" class="flex items-center space-x-0.5 bg-white text-black text-sm p-2 rounded-l-md border-l border-gray-300 border-y">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="hidden selected-link size-5">
                <path stroke-linecap="round" stroke-linejoin="round" d="M13.19 8.688a4.5 4.5 0 0 1 1.242 7.244l-4.5 4.5a4.5 4.5 0 0 1-6.364-6.364l1.757-1.757m13.35-.622 1.757-1.757a4.5 4.5 0 0 0-6.364-6.364l-4.5 4.5a4.5 4.5 0 0 0 1.242 7.244" />
              </svg>
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="hidden selected-page size-5">
                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
              </svg>
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="hidden selected-email size-5">
                <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75" />
              </svg>
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="hidden selected-phone size-5">
                <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 1.5H8.25A2.25 2.25 0 0 0 6 3.75v16.5a2.25 2.25 0 0 0 2.25 2.25h7.5A2.25 2.25 0 0 0 18 20.25V3.75a2.25 2.25 0 0 0-2.25-2.25H13.5m-3 0V3h3V1.5m-3 0h3m-3 18.75h3" />
              </svg>
              
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
                <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
              </svg>
            </button>
            <div data-dropdown-target="menu" class="hidden absolute top-4 left-0 z-10 mt-5 flex w-screen max-w-max p-3 bg-white rounded border">
              <div class="text-sm overflow-hidden w-36">
                <label class='justify-between no-underline flex px-3 py-2 rounded text-gray-900 bg-white hover:bg-gray-100'>
                  <div class="flex space-x-1.5">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-5">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
                    </svg>
                    <span>Stránka</span>
                  </div>

                  <%= b.radio_button :secondary_button_link_type, "page", { 'data-action': 'dropdown#change dropdown#toggle', 'data-dropdown-target': 'input' } %>
                </label>
                <label class='justify-between no-underline flex px-3 py-2 rounded text-gray-900 bg-white hover:bg-gray-100'>
                  <div class="flex space-x-1.5">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-5">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M13.19 8.688a4.5 4.5 0 0 1 1.242 7.244l-4.5 4.5a4.5 4.5 0 0 1-6.364-6.364l1.757-1.757m13.35-.622 1.757-1.757a4.5 4.5 0 0 0-6.364-6.364l-4.5 4.5a4.5 4.5 0 0 0 1.242 7.244" />
                    </svg>
                    <span>URL</span>
                  </div>
                  <%= b.radio_button :secondary_button_link_type, "link", { 'data-action': 'dropdown#change dropdown#toggle', 'data-dropdown-target': 'input' } %>
                </label>

                <label class='justify-between no-underline flex items-center px-2 py-2 rounded text-gray-900 bg-white hover:bg-gray-100'>
                  <div class="flex space-x-1.5">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-5">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75" />
                    </svg>
                    <span>E-mail</span>
                  </div>
                  <%= b.radio_button :secondary_button_link_type, "email", { 'data-action': 'dropdown#change dropdown#toggle', 'data-dropdown-target': 'input', class: "radio radio-xs" } %>
                </label>
                <label class='justify-between no-underline flex items-center px-2 py-2 rounded text-gray-900 bg-white hover:bg-gray-100'>
                  <div class="flex space-x-1.5">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-5">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 1.5H8.25A2.25 2.25 0 0 0 6 3.75v16.5a2.25 2.25 0 0 0 2.25 2.25h7.5A2.25 2.25 0 0 0 18 20.25V3.75a2.25 2.25 0 0 0-2.25-2.25H13.5m-3 0V3h3V1.5m-3 0h3m-3 18.75h3" />
                    </svg>

                    <span>Telefon</span>
                  </div>
                  <%= b.radio_button :secondary_button_link_type, "phone", { 'data-action': 'dropdown#change dropdown#toggle', 'data-dropdown-target': 'input', class: "radio radio-xs" } %>
                </label>
              </div>
            </div>

            <div class="selected-page hidden w-full">
              <%= b.combobox :secondary_button_page_id, Page.all, value: b.object.secondary_button_page_id.to_i, placeholder: "Vyberte stránku" %>
            </div>

            <div class="selected-link hidden w-full">
              <%= b.text_field :secondary_button_link, placeholder: "Odkaz", class: "w-full input rounded-none rounded-r" %>
            </div>

            <div class="selected-email hidden w-full">
              <%= b.email_field :secondary_button_email, placeholder: "E-mail", class: "w-full input rounded-none rounded-r" %>
            </div>

            <div class="selected-phone hidden w-full">
              <%= b.telephone_field :secondary_button_phone, placeholder: "Telefon", class: "w-full input rounded-none rounded-r" %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>