<div class="bg-base-100 border border-base-300 px-4 py-4 rounded-box">
  <label class="label label-sm font-normal">Oddělovač</label>

  <%= b.hidden_field :content, value: b.object.content.to_json, id: "tiptap_json_input_#{b.object.id}" %>
  <%= b.hidden_field :text, id: "tiptap_html_input_#{b.object.id}" %>

  <div class="w-full flex justify-between items-center space-x-3">
    <label class="fieldset-label text-gray-700 w-1/2">Velikost</label>
    <div class="flex-1" data-controller="slider" data-slider-element-value="control-<%= b.object.id %>" data-slider-current-value="<%= b.object.width %>" data-slider-values-value='["w-24", "w-36", "w-48", "w-64", "w-1/2", "w-full"]'>
      <div data-slider-target="handler" class="slider-styled slider-round"></div>
      <%= b.hidden_field :width, value: b.object.width, min: 1, max: 6, data: { 'slider-target': "input" }, class: "w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700" %>
    </div>
  </div>
</div>