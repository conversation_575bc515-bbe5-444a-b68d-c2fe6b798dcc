<% content_for :title do %>
  <%= @owner.title || "Untiteled" %>
<% end %>

<style>
  .profiler-results{
    display: none;
  }
</style>

<div class="flex">
  <%= turbo_frame_tag :sidebar %>
  <div class="flex-1">
    <div class="blocks-container">
       <% if @blocks.empty? %>
          <div class="flex h-screen w-full flex-col space-y-3 items-center justify-center">
              <h2 class="text-base font-semibold leading-6 text-gray-900">Na stránce zatím nejsou žádn<PERSON> bloky</h2>
              <p class="mt-1 px-4 text-sm text-gray-500">
                Pomocí bloků vytváříte obsah stránky. Bloky mohou obsahovat text, obr<PERSON><PERSON><PERSON>, videa, formul<PERSON><PERSON>e a dalš<PERSON> prvky.
              </p>

              <a href="<%= new_admin_content_page_block_path(@owner) %>" class="text-center p-2 rounded-md hover:bg-gray-100" data-turbo-frame="sidebar">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                </svg>
              </a>
          </div>
      <% end %>

      <% block_count = @blocks.size %>

      <% @blocks.each_with_index do |block, index| %>
        <%= render "block",
                   block: block,
                   component: @block_components[block.id],
                   owner: @page,
                   first: index.zero?,
                   last: index == block_count - 1
        %>
      <% end %>
    </div>
  </div>
</div>