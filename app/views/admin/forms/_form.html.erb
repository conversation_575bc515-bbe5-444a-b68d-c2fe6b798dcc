<%= form_with model: [:admin, form] do |f| %>
  <% if f.object.errors.any? %>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
      <strong class="font-bold">Vyskytly se chyby:</strong>
      <ul class="list-disc ml-5">
        <% f.object.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="bg-white p-6 border rounded-lg">
    <h2 class="text-lg font-semibold text-gray-900 border-b pb-4">Základní nastavení formuláře</h2>

    <div class="mt-4 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
      <div class="sm:col-span-4">
        <%= f.label :name, "Název formuláře", class: "block text-sm font-medium leading-6 text-gray-900" %>
        <div class="mt-2">
          <%= f.text_field :name, class: "input w-full" %>
        </div>
      </div>

      <div class="sm:col-span-2">
        <%= f.label :type, "Typ formuláře", class: "block text-sm font-medium leading-6 text-gray-900" %>
        <div class="mt-2">
          <%= f.select :type, [['Kontaktní', 'ContactForm'], ['Rezervační', 'BookingForm']], { include_blank: 'Vyberte typ' }, class: "select select-bordered w-full" %>
        </div>
      </div>

      <div class="col-span-full">
        <%= f.label :description, "Popis (interní poznámka)", class: "block text-sm font-medium leading-6 text-gray-900" %>
        <div class="mt-2">
          <%= f.text_area :description, rows: 3, class: "textarea textarea-bordered w-full" %>
        </div>
      </div>
    </div>
  </div>

  <div class="mt-8" data-controller="nested-form" data-nested-form-wrapper-selector-value=".nested-form-wrapper">
    <h2 class="text-lg font-semibold text-gray-900">Pole formuláře</h2>

    <template data-nested-form-target="template">
      <%= f.fields_for :form_fields, FormField.new, child_index: 'NEW_RECORD' do |ff| %>
        <%= render 'form_field', f: ff %>
      <% end %>
    </template>

    <div class="mt-4 flex flex-col space-y-3" data-controller="sortable" data-sortable-handle-value=".handle">
      <%= f.fields_for :form_fields do |ff| %>
        <%= render 'form_field', f: ff %>
      <% end %>

      <div data-nested-form-target="target"></div>
    </div>

    <div class="mt-4">
      <button type="button" data-action="nested-form#add" class="button-secondary">
        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path d="M10.75 4.75a.75.75 0 0 0-1.5 0v4.5h-4.5a.75.75 0 0 0 0 1.5h4.5v4.5a.75.75 0 0 0 1.5 0v-4.5h4.5a.75.75 0 0 0 0-1.5h-4.5v-4.5z" /></svg>
        Přidat pole
      </button>
    </div>
  </div>

  <div class="py-5 mt-6 border-t border-gray-200">
    <div class="flex justify-end">
      <%= link_to "Zrušit", admin_forms_path, class: "btn btn-ghost" %>
      <button type="submit" class="btn btn-primary ml-3">
        Uložit formulář
      </button>
    </div>
  </div>
<% end %>