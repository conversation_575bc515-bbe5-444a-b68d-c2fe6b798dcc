<%= content_for :action_toolbar do %>
  <div class="flex space-x-2">
    <a href="<%= new_admin_form_path(type: 'content') %>" class="btn btn-primary">
      <svg class="-ml-1 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
      </svg>
      Přidat formulář
    </a>
  </div>
<% end %>

<div class="sm:flex sm:items-center">
  <div class="sm:flex-auto">
    <h1 class="text-base font-semibold text-gray-900"><PERSON><PERSON><PERSON><PERSON></h1>
  </div>
</div>

<div class="mt-4">
<% if @forms.empty? %>
  <div class="bg-blue-50 p-4 rounded-lg">
    <div class="flex">
      <div class="flex-shrink-0">
        <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
        </svg>
      </div>
      <div class="ml-3">
        <h3 class="text-sm font-medium text-blue-800">Zatím jste nevytvořili žádný formulář.</h3>
        <div class="mt-2 text-sm text-blue-700">
          <p>Kliknutím na tlačítko "Přidat formulář" můžete začít vytvářet svůj první kontaktní nebo rezervační formulář.</p>
        </div>
      </div>
    </div>
  </div>
<% else %>
  <%# Zde začíná nová část s kartami %>
  <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
    <% @forms.each do |form| %>
      <div class="flex flex-col bg-white border border-gray-200 shadow-sm rounded-lg overflow-hidden">

        <%# Hlavička karty %>
        <div class="px-4 py-4 sm:px-6">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 truncate">
              <%= form.name %>
            </h3>
            <div class="ml-2 flex-shrink-0 flex">
              <span class="inline-flex items-center rounded-md bg-gray-100 px-2 py-1 text-xs font-medium text-gray-600">
                <%= form.type.gsub('Form', '') %>
              </span>
              <% if form.try(:booking_type) %>
                <span class="ml-1 inline-flex items-center rounded-md bg-blue-100 px-2 py-1 text-xs font-medium text-blue-700">
                  <%= form.booking_type_humanize %>
                </span>
              <% end %>
            </div>
          </div>
        </div>

        <%# Tělo karty se statistikami %>
        <div class="border-t border-gray-200 px-4 py-4 sm:px-6 grow">
          <dl class="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2">
            <div class="sm:col-span-2">
              <dt class="text-sm font-medium text-gray-500">Celkem odeslání</dt>
              <dd class="mt-1 text-2xl font-semibold text-gray-900">
                <%= form.total_submissions_count %>
              </dd>
            </div>
            <%# Zde by mohly být další statistiky, např. nepřečtené %>
            <div class="sm:col-span-2">
              <dt class="text-sm font-medium text-gray-500">Poslední odeslání</dt>
              <dd class="mt-1 text-sm text-gray-900">
                <%= form.last_submission_at ? l(form.last_submission_at, format: :long) : "Zatím žádné" %>
              </dd>
            </div>
          </dl>
        </div>

        <%# Patička karty s akcemi %>
        <div class="border-t border-gray-200 bg-gray-50 px-4 py-3 sm:px-6">
          <div class="flex justify-between items-center">
            <%# Odkaz na přehled odeslání %>
            <%= link_to "Zobrazit odeslání", 'admin_form_form_submissions_path(form)', class: "button-secondary" %>

            <%# Ikonky pro editaci a smazání %>
            <div class="flex items-center space-x-2">
              <%= link_to edit_admin_form_path(form), class: 'edit-link' do %>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-500 hover:text-gray-800" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" /></svg>
              <% end %>
              <%= button_to admin_form_path(form), method: :delete, data: { turbo_confirm: "Opravdu chcete formulář '#{form.name}' smazat?" }, class: 'remove-link' do %>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-500 hover:text-red-800" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" /></svg>
              <% end %>
            </div>
          </div>
        </div>

      </div>
    <% end %>
  </div>
<% end %>
</div>