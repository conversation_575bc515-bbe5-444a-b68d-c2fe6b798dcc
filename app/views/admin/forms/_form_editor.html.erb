<div class="card bg-white border-0 shadow-sm">
  <div class="card-body p-0">
    <div data-controller="tabs" data-tabs-active-tab-class="-mb-px border-b border-gray-800 text-black">
      <div class="border-b border-gray-200">
        <ul class="flex -mb-px">
          <li data-tabs-target="tab" data-action="click->tabs#change" class="mr-2">
            <button type="button" class="inline-flex items-center gap-2 cursor-pointer py-4 px-4 text-sm no-underline">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path><circle cx="12" cy="12" r="3"></circle></svg>
              Pole formuláře
            </button>
          </li>
          <li data-tabs-target="tab" data-action="click->tabs#change" class="mr-2">
            <button type="button" class="inline-flex items-center gap-2 cursor-pointer py-4 px-4 text-sm no-underline">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path><circle cx="12" cy="12" r="3"></circle></svg>
              Nastavení
            </button>
          </li>
        </ul>
      </div>

      <%# Panel s poli formuláře %>
      <div class="p-6 space-y-6" data-tabs-target="panel" data-controller="nested-form" data-nested-form-wrapper-selector-value=".nested-form-wrapper" data-sortable-handle-value=".handle">
        <div class="flex justify-between items-center">
          <div>
            <h3 class="text-lg font-semibold text-gray-900">Struktura formuláře</h3>
            <p class="text-sm text-gray-600 mt-1">Přidejte a uspořádejte pole formuláře.</p>
          </div>
          <button type="button" data-action="nested-form#add" class="btn btn-sm btn-primary">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 mr-2"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg>
            Přidat pole
          </button>
        </div>

        <template data-nested-form-target="template">
          <%= f.fields_for :form_fields, FormField.new, child_index: 'NEW_RECORD' do |ff| %>
            <%= render 'form_field', f: ff, form: @form %>
          <% end %>
        </template>

        <div class="space-y-4" data-controller="sortable" data-nested-form-target="target">
          <%= f.fields_for :form_fields do |ff| %>
            <%= render 'form_field', f: ff, form: @form %>
          <% end %>
        </div>
      </div>

      <%# Panel s nastavením formuláře %>
      <div class="hidden p-6" data-tabs-target="panel">
        <div class="form-control w-full mt-4">
          <label class="label"><span class="label-text">Typ formuláře</span></label>
          <%= f.select :type, [['Kontaktní', 'ContactForm'], ['Rezervační', 'BookingForm']], {}, disabled: true, class: "disabled select select-bordered w-full" %>
        </div>

        <div class="form-control w-full mt-4">
          <label class="label"><span class="label-text">Název formuláře</span></label>
          <%= f.text_field :name, class: "input input-bordered w-full" %>
        </div>

        <div class="form-control w-full mt-4">
          <label class="label"><span class="label-text">Text na odesílacím tlačítku</span></label>
          <div class="mt-1 flex flex-col space-y-1.5">
            <% current_tenant.available_locales.each do |locale| %>
              <div class="flex space-x-2 items-center">
                <img src="<%= asset_path("flags/#{locale}.svg") %>" class="w-4 h-4 rounded-full">
                <%= f.text_field "submit_text_#{locale}", class: "input input-bordered w-full", placeholder: "např. Odeslat zprávu" %>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="py-3 px-4 bg-base-200/80 border-t-2 border-primary/50">
  <div class="mx-auto">
    <button type="submit" class="btn btn-primary">Uložit nastavení</button>
  </div>
</div>