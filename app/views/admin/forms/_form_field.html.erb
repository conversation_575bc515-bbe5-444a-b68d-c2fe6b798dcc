<div data-sortable-update-url="<%= f.object.id ? sort_admin_form_form_field_path(form, f.object.id) : nil %>" data-new-record="<%= f.object.new_record? %>" class="nested-form-wrapper card bg-white border border-gray-200 hover:border-gray-300 transition-colors ">
  <div class="card-body p-6">
    <%= f.label :field_type, "Nastavení pole", class: "text-sm font-medium text-gray-900 mb-1.5 block" %>

    <div class="flex items-center gap-6">
      <div class="handle flex items-center justify-center w-8 h-8 rounded-lg bg-gray-50 hover:bg-gray-100 cursor-grab active:cursor-grabbing transition-colors border border-gray-200">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="w-4 h-4 text-gray-400" viewBox="0 0 16 16"><path d="M7 2a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm5 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0zM7 5a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm5 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0zM7 8a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm5 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm-5 3a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm5 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm-5 3a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm5 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0z"/></svg>
      </div>

      <div class="flex-1 space-y-5">
        <div class="flex items-center justify-between gap-6">
          <div class="flex-1">
            <%= f.select :field_type, options_for_select([['Text', 'text'], ['E-mail', 'email'], ['Textová oblast', 'textarea']], f.object.field_type), {}, { class: "select select-bordered w-full", data: { action: "input->form-preview#update" } } %>
          </div>
          <div class="flex items-center space-x-3 pt-7">
            <%= f.check_box :required, { class: "toggle toggle-primary", data: { action: "change->form-preview#update" } } %>
            <%= f.label :required, "Povinné", class: "text-sm font-medium text-gray-900" %>
          </div>
        </div>
      </div>

      <% unless f.object.custom? %>
        <button type="button" data-action="nested-form#remove" class="btn btn-ghost btn-square btn-sm">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 text-red-600"><path d="M3 6h18"></path><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path><line x1="10" x2="10" y1="11" y2="17"></line><line x1="14" x2="14" y1="11" y2="17"></line></svg>
        </button>
      <% end %>
    </div>

    <div class="mt-4 sm:ml-16">
      <label class="text-sm font-medium text-gray-900 mb-3 block">Název pole</label>
      <div class="space-y-3">
        <% current_tenant.available_locales.each do |locale| %>
          <div class="flex items-center gap-3">
            <div class="flex items-center gap-2 min-w-[110px]">
              <img src="<%= asset_path("flags/#{locale}.svg") %>" class="w-5 h-5 rounded-full" title="<%= locale.to_s.upcase %>">
            </div>
            <%= f.text_field "label_#{locale}", class: "input input-bordered w-full", placeholder: "Název pole", data: { action: "input->form-preview#update", locale: locale } %>
          </div>
        <% end %>
      </div>
    </div>
  </div>
  <%= f.hidden_field :id %>
  <%= f.hidden_field :position, data: { sortable_target: "position" } %>
  <%= f.hidden_field :_destroy %>
</div>