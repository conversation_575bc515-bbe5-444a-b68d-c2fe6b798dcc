<div class="relative bg-white flex">
  <div class="flex-1 focus:outline-none">
    <div class="relative max-w-6xl mx-auto md:px-8 xl:px-0">
      <div class="py-6">
        <div class="px-4 sm:px-6 md:px-0">
          <h2 class="text-lg font-medium text-gray-900 mb-6">Spr<PERSON><PERSON><PERSON></h2>
          
          <!-- Seznam současných správců -->
          <div class="mb-8">
            <h3 class="text-md font-medium text-gray-900 mb-4">Aktivn<PERSON> správci</h3>
            <% if @users.any? %>
              <div class="bg-white shadow overflow-hidden sm:rounded-md">
                <ul role="list" class="divide-y divide-gray-200">
                  <% @users.each do |user| %>
                    <li class="px-6 py-4">
                      <div class="flex items-center justify-between">
                        <div class="flex items-center">
                          <div class="flex-shrink-0">
                            <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                              <svg class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                              </svg>
                            </div>
                          </div>
                          <div class="ml-4">
                            <div class="text-sm font-medium text-gray-900">
                              <%= user.name.presence || user.email %>
                            </div>
                            <div class="text-sm text-gray-500">
                              <%= user.email %>
                              <% if user == current_user %>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 ml-2">
                                  Vy
                                </span>
                              <% end %>
                            </div>
                          </div>
                        </div>
                        <div class="flex items-center">
                          <% unless user == current_user %>
                            <%= link_to "Odebrat", 
                                admin_settings_user_path(user), 
                                method: :delete,
                                data: { 
                                  confirm: "Opravdu chcete odebrat správce #{user.name || user.email}?" 
                                },
                                class: "text-red-600 hover:text-red-900 text-sm font-medium" %>
                          <% end %>
                        </div>
                      </div>
                    </li>
                  <% end %>
                </ul>
              </div>
            <% else %>
              <p class="text-gray-500 text-sm">Žádní správci.</p>
            <% end %>
          </div>

          <!-- Formulář pro novou pozvánku -->
          <div class="mb-8">
            <h3 class="text-md font-medium text-gray-900 mb-4">Pozvat nového správce</h3>
            <%= form_with model: Invitation.new, url: create_invitation_admin_settings_users_path, local: true, class: "space-y-4" do |f| %>
              <div class="flex space-x-4">
                <div class="flex-1">
                  <%= f.email_field :email, 
                      class: "input", 
                      placeholder: "<EMAIL>",
                      required: true %>
                  <% if @invitation&.errors&.[](:email)&.any? %>
                    <p class="form-input-error mt-1">
                      <%= @invitation.errors[:email].first %>
                    </p>
                  <% end %>
                </div>
                <div>
                  <%= f.submit "Odeslat pozvánku", class: "btn btn-primary" %>
                </div>
              </div>
              <p class="text-sm text-gray-500">
                Na tuto adresu bude odeslána pozvánka do správy účtu.
              </p>
            <% end %>
          </div>

          <!-- Seznam pozvánek -->
          <div>
            <h3 class="text-md font-medium text-gray-900 mb-4">Odeslané pozvánky</h3>
            <% if @invitations.any? %>
              <div class="bg-white shadow overflow-hidden sm:rounded-md">
                <ul role="list" class="divide-y divide-gray-200">
                  <% @invitations.each do |invitation| %>
                    <li class="px-6 py-4">
                      <div class="flex items-center justify-between">
                        <div class="flex items-center">
                          <div class="flex-shrink-0">
                            <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                              <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                              </svg>
                            </div>
                          </div>
                          <div class="ml-4">
                            <div class="text-sm font-medium text-gray-900">
                              <%= invitation.email %>
                            </div>
                            <div class="text-sm text-gray-500">
                              Pozval: <%= invitation.invited_by_name %>
                              • <%= time_ago_in_words(invitation.created_at) %> zpět
                            </div>
                          </div>
                        </div>
                        <div class="flex items-center">
                          <% if invitation.accepted? %>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              Přijato <%= time_ago_in_words(invitation.accepted_at) %> zpět
                            </span>
                          <% elsif invitation.expired? %>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                              Vypršelo
                            </span>
                          <% else %>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                              Čeká na přijetí
                            </span>
                          <% end %>
                        </div>
                      </div>
                    </li>
                  <% end %>
                </ul>
              </div>
            <% else %>
              <p class="text-gray-500 text-sm">Žádné odeslané pozvánky.</p>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
