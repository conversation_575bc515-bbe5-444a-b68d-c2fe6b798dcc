<div class="mb-8">
  <h1 class="text-3xl font-bold text-gray-900">Propojení</h1>
  <p class="mt-2 text-gray-600">
    Propojte svůj web s externími službami pro automatické načítání obsahu a dat.
  </p>
</div>

<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  <% Service::SERVICES.each do |service| %>
    <% existing_service = Service.find_by_type(service[:class_name]) %>
    <% is_active = existing_service&.active? %>
    <div class="card bg-base-100 shadow-lg hover:shadow-xl transition-shadow duration-200">
      <div class="card-body">
        <div class="flex items-start space-x-4">
          <div class="flex-shrink-0">
            <div class="avatar">
              <div class="w-16 h-16 rounded-xl bg-base-200 p-3">
                <img src="/images/services/<%= service[:class_name].underscore %>.svg"
                     alt="<%= service[:name] %>"
                     class="w-full h-full object-contain">
              </div>
            </div>
          </div>
          <div class="flex-1 min-w-0">
            <h3 class="card-title text-lg">
              <%= service[:name] %>
              <% if is_active %>
                <div class="badge badge-success badge-sm">Aktivní</div>
              <% elsif existing_service %>
                <div class="badge badge-warning badge-sm">Neaktivní</div>
              <% else %>
                <div class="badge badge-ghost badge-sm">Nenastaveno</div>
              <% end %>
            </h3>
            <p class="text-sm text-base-content/70 mt-1">
              <%= service[:label] %>
            </p>
            <% if existing_service && !is_active %>
              <p class="text-xs text-warning mt-1">
                Služba je nakonfigurována, ale není aktivní
              </p>
            <% end %>
          </div>
        </div>

        <div class="card-actions justify-end mt-4">
          <%= link_to edit_admin_service_path(service[:slug]),
                      class: "btn btn-primary btn-sm" do %>
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
              <path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.*************.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" />
              <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            Nastavit
          <% end %>
        </div>
      </div>
    </div>
  <% end %>
</div>
</div>