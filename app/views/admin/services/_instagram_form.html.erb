<div class="flex justify-between items-center">
  <div class="px-4 sm:px-6 md:px-0 flex space-x-2 items-center">
    <a href="<%= admin_services_path %>" id="back-link">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
        <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5 8.25 12l7.5-7.5" />
      </svg>
    </a>

    <div class="flex items-center space-x-3">
      <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-pink-500 to-purple-600 p-1.5">
        <img src="/images/services/instagram.svg" alt="Instagram" class="w-full h-full object-contain filter brightness-0 invert">
      </div>
      <h1 class="text-2xl font-semibold text-gray-900">
        Instagram
      </h1>
    </div>
  </div>
</div>

<%= form_with model: @service, url: admin_service_path(:instagram), method: (@service.persisted? ? :patch : :post) do |f| %>
  <% if @service.errors.any? %>
    <div class="bg-red-50 mt-3 text-red-700 p-4 rounded-sm">
      <h2 class="text-lg font-semibold"><%= pluralize(@service.errors.count, "chyba") %> brání v uložení:</h2>
      <ul class="mt-2 list-disc list-inside">
        <% @service.errors.full_messages.each do |message| %>
          <li class="text-sm"><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="mt-4">
    <div class="mt-1">
      <div>
        <div class="flex space-x-4 mt-4">
          <div class="relative w-full">
            <div class="flex-1 focus:outline-none bg-white border border-gray-100 w-full">
              <div class="relative mx-auto px-4">
                <div class="py-4">

                  <!-- Typ služby -->
                  <div class="mb-4">
                    <label class="label">Typ služby</label>
                    <div class="mt-2 p-2 bg-gray-50 border border-gray-200 rounded-md">
                      <div class="flex items-center space-x-2">
                        <div class="w-5 h-5 rounded bg-gradient-to-br from-pink-500 to-purple-600 p-1">
                          <img src="/images/services/instagram.svg" alt="Instagram" class="w-full h-full object-contain filter brightness-0 invert">
                        </div>
                        <span>Instagram</span>
                      </div>
                    </div>
                  </div>

                  <!-- Aktivace služby -->
                  <div class="mb-4">
                    <label class="label">Aktivace</label>
                    <div class="mt-2">
                      <label class="cursor-pointer flex items-center space-x-3">
                        <%= f.check_box :active, class: "toggle toggle-primary" %>
                        <span class="text-sm">Aktivovat Instagram propojení</span>
                      </label>
                      <p class="text-xs text-gray-500 mt-1">Zapněte pro automatické načítání příspěvků z Instagramu</p>
                    </div>
                  </div>

                  <!-- URL pole -->
                  <div class="mb-4">
                    <label class="label">URL Instagram profilu</label>
                    <div class="mt-1">
                      <%= f.text_field :instagram_url, 
                          class: "input w-full #{@service.errors[:instagram_url].any? ? 'border-red-500' : ''}", 
                          placeholder: "https://www.instagram.com/vase_uzivatelske_jmeno/" %>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">Zadejte úplnou URL adresu vašeho Instagram profilu</p>
                    <% if @service.errors[:instagram_url].any? %>
                      <p class="text-xs text-red-500 mt-1">
                        <%= @service.errors[:instagram_url].first %>
                      </p>
                    <% end %>
                  </div>

                  <!-- Nápověda -->
                  <div class="bg-blue-50 border border-blue-200 rounded-md p-3 mt-4">
                    <h4 class="text-sm font-medium text-blue-900 mb-2">Jak najít URL vašeho Instagram profilu:</h4>
                    <ol class="text-xs text-blue-800 space-y-1 list-decimal list-inside">
                      <li>Přejděte na <strong>instagram.com</strong> a přihlaste se</li>
                      <li>Klikněte na svůj profil</li>
                      <li>Zkopírujte URL z adresního řádku prohlížeče</li>
                    </ol>
                  </div>

                  <!-- Submit button -->
                  <div class="mt-6 flex justify-end">
                    <%= f.submit "Uložit nastavení", class: "bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded cursor-pointer" %>
                  </div>

                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
<% end %>
