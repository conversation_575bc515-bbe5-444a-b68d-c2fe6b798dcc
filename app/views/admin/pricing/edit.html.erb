<div class="flex">
  <div class="px-4 sm:px-6 md:px-0 flex space-x-2 items-center">
    <a href="<%= admin_pricing_index_path %>" id="back-link" data-form-target="backLink">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
        <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5 8.25 12l7.5-7.5" />
      </svg>
    </a>

    <h1 class="text-2xl font-semibold text-gray-900">
      <%= @pricing.name %>
    </h1>
  </div>
</div>

<div class="mt-4" data-controller="tabs" data-tabs-active-tab-class="-mb-px border-b-2 border-gray-800 text-black">
  <ul class="list-reset flex border-b border-gray-200 text-gray-800 sticky top-0 bg-gray-50 z-10">
      <li class="mr-1 hover:bg-gray-100" data-tabs-target="tab" data-action="click->tabs#change:prevent">
        <button type="button" class="inline-flex space-x-2 items-center cursor-pointer py-2 px-4 text-sm no-underline">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-5">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z" />
          </svg>

          <span>Základní</span>
        </button>
      </li>

    <li class="mr-1 hover:bg-gray-100" data-tabs-target="tab" data-action="click->tabs#change:prevent">
      <button type="button" class="inline-flex space-x-2 items-center cursor-pointer py-2 px-4 text-sm no-underline">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-5">
          <path stroke-linecap="round" stroke-linejoin="round" d="m10.5 21 5.25-11.25L21 21m-9-3h7.5M3 5.621a48.474 48.474 0 0 1 6-.371m0 0c1.12 0 2.233.038 3.334.114M9 5.25V3m3.334 2.364C11.176 10.658 7.69 15.08 3 17.502m9.334-12.138c.896.061 1.785.147 2.666.257m-4.589 8.495a18.023 18.023 0 0 1-3.827-5.802" />
        </svg>

        <span>Překlady</span>
      </button>
    </li>
  </ul>

  <div class="hidden py-4" data-tabs-target="panel">
    <%= render 'form', pricing: @pricing %>
  </div>

  <div class="hidden py-4" data-tabs-target="panel">
      <%= form_with model: [:admin, @pricing], url: admin_pricing_path(@pricing), method: :patch, html: { class: "p-4 bg-gray-50 border border-t-0 border-gray-200" } do |f| %>
        <%# Iterujeme přes VŠECHNY sekce a vytváříme pro ně editační "karty" %>
        <%= f.fields_for :pricing_sections do |section_fields| %>
          <% section = section_fields.object %>
          <div class="bg-white border border-gray-100 p-6 mb-8">

            <%# ---- PŘEKLADY PRO SAMOTNOU SEKCI ---- %>
            <h3 class="font-bold text-lg mb-4 text-gray-800">
              Sekce: <span class="text-primary font-semibold"><%= section.name %></span>
            </h3>
            <div class="grid grid-cols-1 sm:grid-cols-4 gap-4 mb-6 pb-6 border-b">
              <% current_tenant.available_locales.each do |locale| %>
                <div>
                  <%= section_fields.label "name_#{locale}", class: "label-text" do %>
                    Název sekce <span class="font-mono text-xs p-1 bg-gray-200 text-gray-600 rounded"><%= locale %></span>
                  <% end %>
                  <%= section_fields.text_field "name_#{locale}", class: "input input-bordered w-full mt-1" %>
                </div>
              <% end %>
            </div>

            <div class="space-y-4">
              <%= section_fields.fields_for :pricing_items do |item_fields| %>
                <% item = item_fields.object %>
                <div class="grid grid-cols-1 lg:grid-cols-[1.5fr,2fr,2fr] gap-x-6 gap-y-2">
                  <div>
                    <div class="space-y-2">
                      <% current_tenant.available_locales.each do |locale| %>
                        <div class="flex items-center gap-2">
                          <span class="w-10 text-center font-mono text-xs p-1 bg-gray-200 text-gray-600 rounded"><%= locale %></span>
                          <div class="w-1/3">
                          <%= item_fields.text_field "name_#{locale}", class: "input input-sm input-bordered w-full" %>
                          </div>
                          <%= item_fields.text_field "description_#{locale}", class: "input input-sm input-bordered w-full" %>
                        </div>
                      <% end %>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        <% end %>

        <%# Tlačítko pro uložení formuláře s překlady %>
        <div class="mt-8 flex justify-start">
          <button type="submit" class="btn btn-primary">Uložit překlady</button>
        </div>
      <% end %>
    </div>
</div>