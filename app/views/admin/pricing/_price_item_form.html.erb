<div class="nested-form-wrapper"
     data-sortable-update-url="<%= f.object.id ? sort_admin_pricing_pricing_item_path(@pricing, f.object.id) : nil %>"
     data-new-record="<%= f.object.new_record? %>"
     data-controller="price-toggle"
     data-price-toggle-is-textual-value="<%= f.object.is_price_textual? %>">

  <div class="flex items-center space-x-2 w-full">

    <div class="w-8 flex-shrink-0 flex items-center justify-center">
      <% if f.object.persisted? %>
        <button type="button" class="handle text-gray-400 cursor-grab p-1.5 hover:bg-gray-100 rounded-full" title="Změnit pořadí">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4"><path fill-rule="evenodd" d="M13.78 10.47a.75.75 0 0 1 0 1.06l-2.25 2.25a.75.75 0 0 1-1.06 0l-2.25-2.25a.75.75 0 1 1 1.06-1.06l.97.97V5.75a.75.75 0 0 1 1.5 0v5.69l.97-.97a.75.75 0 0 1 1.06 0ZM2.22 5.53a.75.75 0 0 1 0-1.06l2.25-2.25a.75.75 0 0 1 1.06 0l2.25 2.25a.75.75 0 0 1-1.06 1.06l-.97-.97v5.69a.75.75 0 0 1-1.5 0V4.56l-.97.97a.75.75 0 0 1-1.06 0Z" clip-rule="evenodd" /></svg>
        </button>
      <% end %>
    </div>

    <div class="w-10" data-controller="image-preview">
      <div class="relative group">
        <div data-image-preview-target="placeholder"
             class="w-10 h-10 bg-gray-100 border border-gray-300 rounded flex items-center justify-center cursor-pointer hover:bg-gray-200 <%= 'hidden' if f.object.image.attached? %>"
             data-action="click->image-preview#openFileDialog">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 text-gray-400">
            <path stroke-linecap="round" stroke-linejoin="round" d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
          </svg>
        </div>

        <div data-image-preview-target="imageWrapper" class="<%= 'hidden' unless f.object.image.attached? %>">
          <div class="w-10 h-10 cursor-pointer hover:opacity-80" data-action="click->image-preview#openFileDialog">
            <img data-image-preview-target="image"
                 src="<%= f.object.image.attached? ? url_for(f.object.image.variant(:thumb)) : '' %>"
                 class="w-10 h-10 rounded object-cover border border-gray-300" />
          </div>
          <button type="button" data-image-preview-target="removeButton" class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white rounded-full text-xs opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center hover:bg-red-600 hidden" data-action="click->image-preview#clearSelection" title="Zrušit výběr">
            ×
          </button>
        </div>

        <% if f.object.persisted? && f.object.image.attached? %>
          <%= link_to delete_image_admin_pricing_pricing_item_path(@pricing, f.object),
                      data: { turbo_method: :delete, action: "click->image-preview#showPlaceholder" },
                      class: "absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white rounded-full text-xs opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center hover:bg-red-600",
                      title: "Odstranit obrázek" do %>
            ×
          <% end %>
        <% end %>

        <%= f.file_field :image, data: { image_preview_target: "input", action: "change->image-preview#preview" }, class: "hidden", accept: "image/*" %>
      </div>
    </div>

    <div class="w-1/4">
      <%= f.text_field :name, class: "input w-full" %>
    </div>

    <div class="flex items-center space-x-2" data-price-toggle-target="numericWrapper">
      <div class="w-24">
        <%= f.text_field :price, class: "input w-full", placeholder: "Cena" %>
      </div>
      <div class="w-24">
        <%= f.text_field :price_eur, class: "input w-full", placeholder: "Cena EUR" %>
      </div>
    </div>

    <div class="w-[12.5rem]" data-price-toggle-target="textualWrapper">
      <%= f.text_field :price_textual, class: "input w-full", placeholder: "Textová cena, např. Dohodou" %>
    </div>

    <div class="w-20">
      <%= f.text_field :duration, class: "input w-full" %>
    </div>

    <div class="flex-grow">
      <%= f.text_field :description, class: "input w-full" %>
    </div>

    <div class="pr-3 flex items-center space-x-1">
      <label class="text-gray-400 cursor-pointer p-1.5 hover:bg-gray-100 rounded-full"
             data-controller="tippy"
             data-tippy-content="Přepnout typ ceny text / částka">

        <%= f.check_box :is_price_textual, { "data-action": "change->price-toggle#toggle", class: "hidden" }, "true", "false" %>

        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="size-5">
          <path d="M10.75 10.818v2.614A3.13 3.13 0 0 0 11.888 13c.482-.315.612-.648.612-.875 0-.227-.13-.56-.612-.875a3.13 3.13 0 0 0-1.138-.432ZM8.33 8.62c.**************.**************.46.284.736.363V6.603a2.45 2.45 0 0 0-.35.13c-.14.065-.27.143-.386.233-.377.292-.514.627-.514.909 0 .**************.592.**************.128.152Z" /><path fill-rule="evenodd" d="M18 10a8 8 0 1 1-16 0 8 8 0 0 1 16 0Zm-8-6a.75.75 0 0 1 .75.75v.316a3.78 3.78 0 0 1 1.653.713c.426.33.744.74.925 1.2a.75.75 0 0 1-1.395.55 1.35 1.35 0 0 0-.447-.563 2.187 2.187 0 0 0-.736-.363V9.3c.698.093 1.383.32 1.959.696.787.514 1.29 1.27 1.29 2.13 0 .86-.504 1.616-1.29 2.13-.576.377-1.261.603-1.96.696v.299a.75.75 0 1 1-1.5 0v-.3c-.697-.092-1.382-.318-1.958-.695-.482-.315-.857-.717-1.078-1.188a.75.75 0 1 1 1.359-.636c.08.173.245.376.54.569.313.205.706.353 1.138.432v-2.748a3.782 3.782 0 0 1-1.653-.713C6.9 9.433 6.5 8.681 6.5 7.875c0-.805.4-1.558 1.097-2.096a3.78 3.78 0 0 1 1.653-.713V4.75A.75.75 0 0 1 10 4Z" clip-rule="evenodd" /></svg>
      </label>

      <% if f.object.persisted? %>
        <a href="<%= edit_admin_pricing_item_path(f.object) %>" data-controller="tippy" data-tippy-content="Detail služby" class="text-gray-400 cursor-pointer p-1.5 hover:bg-gray-100 rounded-full">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="size-5"><path d="m5.433 13.917 1.262-3.155A4 4 0 0 1 7.58 9.42l6.92-6.918a2.121 2.121 0 0 1 3 3l-6.92 6.918c-.383.383-.84.685-1.343.886l-3.154 1.262a.5.5 0 0 1-.65-.65Z" /><path d="M3.5 5.75c0-.69.56-1.25 1.25-1.25H10A.75.75 0 0 0 10 3H4.75A2.75 2.75 0 0 0 2 5.75v9.5A2.75 2.75 0 0 0 4.75 18h9.5A2.75 2.75 0 0 0 17 15.25V10a.75.75 0 0 0-1.5 0v5.25c0 .69-.56 1.25-1.25 1.25h-9.5c-.69 0-1.25-.56-1.25-1.25v-9.5Z" /></svg>
        </a>
      <% end %>

      <button type="button" data-action="nested-form#remove" class="text-red-500 cursor-pointer p-1.5 hover:bg-red-50 rounded-full">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4"><path d="M5.28 4.22a.75.75 0 0 0-1.06 1.06L6.94 8l-2.72 2.72a.75.75 0 1 0 1.06 1.06L8 9.06l2.72 2.72a.75.75 0 1 0 1.06-1.06L9.06 8l2.72-2.72a.75.75 0 0 0-1.06-1.06L8 6.94 5.28 4.22Z" /></svg>
      </button>
    </div>
  </div>

  <%= f.hidden_field :id if f.object.persisted? %>
  <%= f.hidden_field :position %>
  <%= f.hidden_field :_destroy %>
</div>
