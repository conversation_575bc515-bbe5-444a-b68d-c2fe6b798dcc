<%= content_for :action_toolbar do %>
  <a href="<%= new_admin_pricing_path %>" class="button">
    <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
      <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
    </svg>
    Přídat ceník
  </a>
<% end %>

  <% unless @pricing.any? %>
    <div class="bg-red-50 p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Aktuálně není aktivní žádný ceník.</h3>
        </div>
      </div>
    </div>
<% end %>



  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-base font-semibold text-gray-900">Ceník služeb</h1>
    </div>
  </div>
  <div class="mt-5 flow-root">
    <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
        <table class="min-w-full border-x border-b border-gray-300">
          <% @pricing_by_locale.each do |locale, items| %>
            <thead class="bg-avocado-50 border-t border-gray-300">
              <tr>
                <th scope="col" class="py-2 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-3 flex items-center space-x-1.5">
                  <span>Název</span>
                </th>
                <th scope="col" class="py-2 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-3">Platnost</th>
                <th scope="col" class="py-2 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"></th>
              </tr>
            </thead>

            <tbody data-controller="sortable" class="bg-white">
              <% items.each do |item| %>
                <tr class="border-t border-gray-300 hover:bg-gray-50" data-sortable-update-url="<%= sort_admin_pricing_path(item) %>" data-id="<%= item.id %>">
                  <td class="flex items-center whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                    <button class="handle text-gray-300 p-1.5 hover:text-gray-900">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
                        <path fill-rule="evenodd" d="M2 3.75A.75.75 0 0 1 2.75 3h10.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 3.75ZM2 8a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 8Zm0 4.25a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H2.75a.75.75 0 0 1-.75-.75Z" clip-rule="evenodd" />
                      </svg>
                    </button>
                    <%= item.name %>
                  </td>
                  <td class="whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                    <% if item.valid_from %>
                      <small><%= item.valid_from.strftime("%d.%m.") %> - <%= item.valid_to.strftime("%d.%m.%Y") %></small>
                    <% else %>
                      <small>bez omezení</small>
                    <% end %>
                  </td>
                  <td class="whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3 flex justify-end space-x-2">
                    <%= button_to admin_pricing_path(item), method: :delete, data: { tippy_content: 'Odstranit stránku', turbo_confirm: "Opravdu chcete menu odstranit?" }, class: 'remove-link' do %>
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-800" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    <% end %>
                    <%= link_to edit_admin_pricing_path(item), data: { tippy_content: 'Upravit menu' }, class: 'edit-link' do %>
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    <% end %>
                  </td>
                </tr>
              <% end %>
            </tbody>
          <% end %>
        </table>
      </div>
    </div>
  </div>
</div>


<% if @expired_menus.expired.any? %>
<div class="w-full bg-white border border-gray-200 mt-5">
  <h3 class="py-1.5 px-3 text-lg font-medium text-avocado-700 bg-gray-50">Expirované Ceník</h3>
  <ul class="list-none border-t" data-controller="sortable">
    <% @expired_menus.expired.each do |menu| %>
      <li class="" data-sortable-update-url="<%= sort_admin_pricing_path(menu) %>" data-id="<%= menu.id %>">
        <div class="flex border-b divide-x divide-gray-200 items-center">
          <div class="w-full pl-1.5 font-normal text-gray-900 whitespace-nowrap">
            <div class="flex items-center space-x-1">
              <button class="handle text-gray-300 p-1.5 hover:text-gray-900">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
                  <path fill-rule="evenodd" d="M2 3.75A.75.75 0 0 1 2.75 3h10.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 3.75ZM2 8a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 8Zm0 4.25a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H2.75a.75.75 0 0 1-.75-.75Z" clip-rule="evenodd" />
                </svg>
              </button>
              <span class="text-sm">
                  <a href="<%= edit_admin_pricing_path(menu) %>"> <%= menu.name %></a>
                <% if menu.valid_from %>
                    <small><%= menu.valid_from.strftime("%d.%m.") %> - <%= menu.valid_to.strftime("%d.%m.%Y") %></small>
                  <% end %>
                </span>
            </div>
          </div>

          <div class="px-6 py-1">
          <span class="flex space-x-2 text-right">
             <%= button_to admin_pricing_path(menu), method: :delete, data: { tippy_content: 'Odstranit stránku', turbo_confirm: "Opravdu chcete menu odstranit?" }, class: 'p-1 hover:bg-red-50 rounded-full' do %>
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-800" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            <% end %>

            <%= link_to edit_admin_pricing_path(menu), data: { tippy_content: 'Upravit menu' }, class: 'p-1 hover:bg-avocado-50 rounded-full' do %>
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-avocado-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            <% end %>
          </span>
          </div>
        </div>
      </li>
    <% end %>
  </ul>

<% end %>