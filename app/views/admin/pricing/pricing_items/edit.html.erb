<%# app/views/admin/pricing_items/edit.html.erb %>
  <%= form_with model: [:admin, @pricing_item] do |f| %>
      <div class="px-4 sm:px-6 md:px-0 flex space-x-2 items-center mb-4">
        <a href="<%= edit_admin_pricing_path(@pricing_item.pricing) %>" id="back-link" data-form-target="backLink">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
            <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5 8.25 12l7.5-7.5" />
          </svg>
        </a>

        <h1 class="text-2xl font-semibold text-gray-900">
          <%= @pricing_item.name %>
        </h1>
      </div>

      <%# --- ZOBRAZENÍ CHYB --- %>
      <% if f.object.errors.any? %>
        <div role="alert" class="alert alert-error mb-8 shadow-lg">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
          <div>
            <h3 class="font-bold">Bylo nalezeno <%= f.object.errors.count %> chyb:</h3>
            <ul class="list-disc list-inside text-sm">
              <% f.object.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      <% end %>

      <%# --- HLAVNÍ GRID FORMULÁŘE --- %>
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">

        <div class="lg:col-span-2 space-y-6">
          <div class="card bg-base-100 border border-gray-100">
            <div class="card-body" data-controller="image-preview">
              <h2 class="card-title mb-4">Hlavní obrázek služby</h2>

              <div class="flex justify-between gap-x-12 divide-x divide-gray-200">
                <div class="flex gap-4 items-center pr-8">
                  <div data-image-preview-target="imageWrapper" class="<%= 'hidden' unless f.object.image.attached? %>">
                    <img data-image-preview-target="image"
                         src="<%= f.object.image.attached? ? url_for(f.object.image.variant(:preview)) : '' %>"
                         class="w-32 h-32 rounded-lg object-cover border-4 border-base-300" />
                  </div>
                  <div data-image-preview-target="placeholder" class="w-32 h-32 bg-base-200 rounded-lg flex items-center justify-center <%= 'hidden' if f.object.image.attached? %>">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-base-content/20" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14" /></svg>
                  </div>

                  <div class="flex-grow">
                    <p class="text-sm text-base-content/70 mb-2">Vyberte obrázek, který se bude zobrazovat u detailu služby.</p>
                    <%= f.file_field :image, data: { image_preview_target: "input", action: "change->image-preview#preview" }, class: "file-input file-input-bordered w-full max-w-xs" %>
                  </div>
                </div>

                <div class="flex flex-col self-end gap-4">
                    <div class="flex items-center gap-4">
                      <label class="cursor-pointer">
                        <%= f.radio_button :detail_layout_variant, 'horizontal', class: 'hidden peer', id: "layout_horizontal" %>
                        <div class="w-32 border border-base-300 peer-checked:border-primary rounded-lg p-2 hover:border-primary transition">
                          <div class="flex gap-2">
                            <div class="w-16 h-16 bg-gray-300 rounded"></div>
                            <div class="flex-1 space-y-2">
                              <div class="h-3 bg-gray-300 rounded w-3/4"></div>
                              <div class="h-3 bg-gray-300 rounded w-1/2"></div>
                            </div>
                          </div>
                        </div>
                      </label>

                      <label class="cursor-pointer">
                        <%= f.radio_button :detail_layout_variant, 'vertical', class: 'hidden peer', id: "layout_vertical" %>
                        <div class="w-32 border border-base-300 peer-checked:border-primary rounded-lg p-2 hover:border-primary transition">
                          <div>
                            <div class="w-full h-8 bg-gray-300 rounded mb-2"></div>
                            <div class="space-y-2">
                              <div class="h-2 bg-gray-300 rounded w-3/4 mx-auto"></div>
                              <div class="h-2 bg-gray-300 rounded w-1/2 mx-auto"></div>
                            </div>
                          </div>
                        </div>
                      </label>
                    </div>
                    <p class="text-sm text-base-content/70 text-center">
                      Rozvržení detailu stránky
                    </p>
                  </div>

              </div>
            </div>
          </div>

          <div class="card bg-base-100 border border-gray-100">
            <div class="card-body">
              <h2 class="card-title mb-4">Podrobný popis služby</h2>

              <div data-controller="tabs"
                   data-tabs-active-tab-class="-mb-px border-b-2 border-gray-800 text-black">

                <ul class="list-reset flex border-b border-gray-200 text-gray-800 sticky top-0 bg-base-100 z-10">
                  <% current_tenant.available_locales.each do |locale| %>
                    <li class="mr-1 hover:bg-gray-100" data-tabs-target="tab" data-action="click->tabs#change:prevent">
                      <button class="inline-flex items-center space-x-2 cursor-pointer py-2 px-4 text-sm no-underline">
                        <img src="<%= asset_path("flags/#{locale}.svg") %>" class="w-4 h-4 rounded-full">

                        <span><%= locale.to_s.upcase %></span>
                      </button>
                    </li>
                  <% end %>
                </ul>

                <%# Panely s Tiptap editory %>
                <div class="mt-4 prose prose-lg lg:prose-lg max-w-none text-gray-800">
                  <%# Vytvoříme panel pro každý jazyk %>
                  <% current_tenant.available_locales.each do |locale| %>
                    <%# `hidden` class zajistí, že je panel ve výchozím stavu skrytý %>
                    <div class="hidden"
                         data-tabs-target="panel"
                         data-locale="<%= locale %>"> <%# Přidáno data-locale pro správné párování se záložkou %>

                      <div class="field">
                        <%# Připravíme si unikátní ID pro tento jazyk %>
                        <% json_input_id = "pricing_item_json_content_#{locale}" %>
                        <% html_input_id = "pricing_item_html_content_#{locale}" %>

                        <%# 2. Dynamická skrytá pole %>
                        <%= f.hidden_field :"content_json_#{locale}", value: @pricing_item.public_send("content_json_#{locale}"), id: json_input_id %>
                        <%= f.hidden_field :"content_#{locale}", id: html_input_id %>

                        <%# 3. Dynamické propojení Tiptap controlleru %>
                        <div class="mt-2"
                             data-controller="tiptap"
                             data-tiptap-editor-type-value="article"
                             data-initial-content="<%= @pricing_item.public_send("content_#{locale}").to_json %>"
                             data-json-input-id="<%= json_input_id %>"
                             data-html-input-id="<%= html_input_id %>"
                        >
                          <%# Tiptap editor se automaticky vloží sem %>
                        </div>
                      </div>
                    </div>
                  <% end %>
                </div>

              </div>
            </div>
          </div>
        </div>

        <%# --- LEVÝ SLOUPEC: ZÁKLADNÍ ÚDAJE --- %>
        <div class="lg:col-span-1 space-y-6">
          <div class="card bg-base-100 border border-gray-100">
            <div class="card-body">
              <h2 class="card-title mb-2">Základní údaje</h2>

              <div class="form-control w-full">
                <label class="label"><span class="label-text">Název služby</span></label>

                <div class="mt-1 flex flex-col space-y-1.5">
                  <% current_tenant.available_locales.each do |locale| %>
                    <div class="flex space-x-2 items-center">
                      <img src="<%= asset_path("flags/#{locale}.svg") %>" class="w-4 h-4 rounded-full">
                      <%= f.text_field "name_#{locale}", class: "input input-bordered w-full" %>
                    </div>
                  <% end %>
                </div>
              </div>

              <div class="form-control w-full">
                <label class="label"><span class="label-text">Krátký popis</span></label>

                <div class="mt-1 flex flex-col space-y-1.5">
                  <% current_tenant.available_locales.each do |locale| %>
                    <div class="flex space-x-2 items-center">
                      <img src="<%= asset_path("flags/#{locale}.svg") %>" class="w-4 h-4 rounded-full">
                      <%= f.text_field "description_#{locale}", class: "input input-bordered w-full" %>
                    </div>
                  <% end %>
                </div>
              </div>
            </div>
          </div>

          <div class="card bg-base-100 border border-gray-100"
               data-controller="price-toggle"
               data-price-toggle-is-textual-value="<%= @pricing_item.is_price_textual? %>">
            <div class="card-body">
              <h2 class="card-title mb-2">Další nastavení</h2>

              <div class="flex justify-between items-center">
                <label class="label"><span class="label-text">Cena</span></label>

                <label class="label cursor-pointer gap-2">
                  <span class="label-text">Textová cena</span>
                  <%# Checkbox nyní spouští akci `toggle` na controlleru při změně. %>
                  <%= f.check_box :is_price_textual,
                                  { "data-action": "change->price-toggle#toggle", class: "toggle toggle-primary" },
                                  "true", "false" %>
                </label>
              </div>

              <div class="space-y-2 " data-price-toggle-target="numericWrapper">
                <label class="input w-full">
                  <span class="label">CZK</span>
                  <%= f.text_field :price, class: "grow", placeholder: "Cena v Kč" %>
                </label>

                <label class="input w-full">
                  <span class="label">EUR</span>
                  <%= f.text_field :price_eur, class: "grow", placeholder: "Cena v €" %>
                </label>
              </div>

              <%# Textová cena - target zůstává %>
              <div data-price-toggle-target="textualWrapper">
                <label class="input w-full">
                  <span class="label">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                      <path stroke-linecap="round" stroke-linejoin="round" d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z" />
                    </svg>
                  </span>
                  <%= f.text_field :price_textual, class: "grow", placeholder: "např. Cena dohodou" %>
                </label>
              </div>

              <div class="form-control w-full">
                <label class="label"><span class="label-text">Doba trvání (v minutách)</span></label>
                <%= f.text_field :duration, class: "input input-bordered w-full", placeholder: "např. 60" %>
              </div>
            </div>
          </div>
        </div>
      </div>

  <div class="py-3 px-4 bg-base-200/80 border-t-2 border-primary/50">
    <div class="mx-auto">
      <button type="submit" class="btn btn-primary">Uložit nastavení</button>
    </div>
  </div>
  <% end %>
