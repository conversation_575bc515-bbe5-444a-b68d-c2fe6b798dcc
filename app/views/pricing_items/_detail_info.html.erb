<div class="bg-white/60 p-5 rounded-lg flex items-center gap-x-4">
  <%# Ikona pro cenu (SVG z Heroicons) %>
  <div class="flex-shrink-0">
    <svg class="h-8 w-8 text-accent" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" d="M9.568 3H5.25A2.25 2.25 0 0 0 3 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 0 0 5.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 0 0 9.568 3Z" />
      <path stroke-linecap="round" stroke-linejoin="round" d="M6 6h.008v.008H6V6Z" />
    </svg>
  </div>

  <%# Textový obsah %>
  <div>
    <p class="text-sm opacity-70">Cena</p>
    <p class="text-xl font-bold">
      <% if @pricing_item.is_price_textual? %>
        <%= @pricing_item.price_textual %>
      <% else %>
        <%= number_to_currency(@pricing_item.price, unit: "Kč", separator: ",", delimiter: " ", format: "%n %u") %>
      <% end %>
    </p>
  </div>
</div>

<%# Karta s dobou trvání (zobrazí se pouze pokud je doba uvedena) %>
<% if @pricing_item.duration.present? %>
  <div class="bg-white/60 p-5 rounded-lg flex items-center gap-x-4">
    <%# Ikona pro čas (SVG z Heroicons) %>
    <div class="flex-shrink-0">
      <svg class="h-8 w-8 text-accent" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
      </svg>
    </div>

    <%# Textový obsah %>
    <div>
      <p class="text-sm opacity-70">Doba trvání</p>
      <p class="text-xl font-bold"><%= @pricing_item.duration %> minut</p>
    </div>
  </div>
<% end %>
