<% title @pricing_item.name %>
<% description @pricing_item.description %>

<%# <PERSON><PERSON>š hlavní layout, kter<PERSON> se stará o hlavičku a patičku %>
<%= render @layout_components[:main_header] %>

<%# Nastaví titulek stránky v prohlížeči na název služby %>
<% content_for :title, @pricing_item.name %>

<article class="bg-white">
  <div data-theme="soft">
  <div class="mx-auto max-w-4xl py-6 sm:py-12 px-4 sm:px-6 lg:px-8">
    <header>
      <% if @pricing_item.vertical? %>
        <% if @pricing_item.image.attached? %>
          <%= image_tag @pricing_item.image.variant(:vertical), class: "w-full h-auto max-h-96 object-cover shadow-lg rounded-box mb-8" %>
        <% end %>

        <h1 class="text-2xl lg:text-3xl font-medium">
          <%= @pricing_item.name %>
        </h1>

        <%# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> popis (z pole description), pokud existuje %>
        <% if @pricing_item.description.present? %>
          <p class="mt-2 text-lg text-accent leading-relaxed">
            <%= @pricing_item.description %>
          </p>
        <% end %>

        <div class="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2">
          <%= render partial: "detail_info", locals: { pricing_item: @pricing_item } %>
        </div>
      <% elsif @pricing_item.horizontal? %>
        <div class="flex flex-col md:flex-row  gap-8 items-start">
          <% if @pricing_item.image.attached? %>
            <div class="w-full sm:w-auto">
              <%= image_tag @pricing_item.image.variant(:vertical), class: "sm:hidden w-full h-auto max-h-96 object-cover shadow-lg rounded-box mb-8" %>
              <%= image_tag @pricing_item.image.variant(:horizontal), class: "hidden sm:block w-full h-auto object-cover shadow-lg rounded-box" %>
            </div>
          <% end %>

          <div class="flex-1 w-full">
            <h1 class="text-2xl lg:text-3xl font-medium">
              <%= @pricing_item.name %>
            </h1>

            <% if @pricing_item.description.present? %>
              <p class="mt-2 text-lg text-accent leading-relaxed">
                <%= @pricing_item.description %>
              </p>
            <% end %>

            <div class="mt-6  grid grid-cols-1 sm:grid-cols-2 sm:gap-4">
              <%= render partial: "detail_info", locals: { pricing_item: @pricing_item } %>
            </div>
          </div>
        </div>
      <% end %>
    </header>
  </div>
  </div>
  <div class="mx-auto max-w-4xl py-10 px-4 sm:px-6 lg:px-8">
    <div class="prose prose-lg lg:prose-lg max-w-none text-gray-800">
      <%== @pricing_item.content %>
    </div>
  </div>
</article>

<%# Patička z vašeho layoutu %>
<%#= render @layout_components[:main_footer] %>