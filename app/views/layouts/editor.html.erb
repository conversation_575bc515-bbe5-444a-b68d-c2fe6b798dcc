<!DOCTYPE html>
<html>
<head>
  <title>Editor obsahu | <%= current_tenant.name %></title>
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <%= turbo_refreshes_with method: :morph, scroll: :preserve %>
  <%= yield :head %>
  <%= csrf_meta_tags %>
  <%= csp_meta_tag %>

  <%= google_fonts_link %>

  <%= stylesheet_link_tag "tailwind", "data-turbo-track": "reload" %>
  <%= javascript_include_tag "admin", "data-turbo-track": "reload", type: "module" %>

  <style>
      /* Editor Header Styles */
      .editor-header {
          backdrop-filter: blur(8px);
          background-color: rgba(255, 255, 255, 0.95);
      }

      .editor-header .language-switcher {
          transition: all 0.2s ease-in-out;
      }

      .editor-header .language-switcher:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .editor-header .action-button {
          cursor: pointer;
      }


      .hw-combobox__main__wrapper {
          width: 100% !important;
      }

      .hw-combobox__main__wrapper:focus-within {
          outline: 2px solid black;
      }

      .hw-combobox {
          width: 100% !important;
      }

      .hw-combobox__main__wrapper {
          width: 100% !important;
          background: #fff;
      }

      .hw-combobox-in-group .hw-combobox__main__wrapper {
          border-radius: 0 5px 5px 0 !important;
      }

       .hw-combobox__input {
          background: transparent !important;
      }
  </style>

  <style>
      .image-small {
          width: 100px; /* Adjust size as needed */
      }

      .image-large {
          width: 500px; /* Adjust size as needed */
      }

      .image-full {
          width: 100%; /* Full width */
      }

      .slider-styled,
      .slider-styled .noUi-handle {
          box-shadow: none;
      }

      /* Hide markers on slider handles */
      .slider-styled .noUi-handle::before,
      .slider-styled .noUi-handle::after {
          display: none;
      }

      .slider-round {
          height: 10px !important;
      }

      .slider-round .noUi-connect {
          background: #000000 !important;
      }

      .slider-round .noUi-handle {
          height: 18px !important;
          width: 18px !important;
          top: -5px !important;
          right: -9px !important; /* half the width */
          border-radius: 9px !important;
      }

      .slider-styled .noUi-tooltip {
          display: none;
      }
      .slider-styled .noUi-active .noUi-tooltip {
          display: block;
          font-size: 12px;
          padding: 0 5px;
          background: black;
          color: white;
          border: none;
          border-radius: 5px;
      }

      /* Dropdown se zobrazením ikon */
      .dropdown-content {
          position: absolute;
          top: 100%;
          left: 0;
          background: #fff;
          border: 1px solid #ccc;
          padding: 0.5rem;
          border-radius: 0.25rem;
          z-index: 9999999;
          display: none;
          min-width: 100%;
      }
      .dropdown-content.active {
          display: grid;
      }

      .tiptap p {
          margin: 0.5rem 0;
      }
  </style>

  <%= render "shared/theme_styles" %>

  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.8.1/nouislider.min.css">
  <link href="https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/css/splide.min.css" rel="stylesheet">

  <%= combobox_style_tag %>
</head>

<body data-theme="admin" data-controller="mobile-preview">

<%= render "shared/editor_header" %>

<div data-mobile-preview-target="iframeContainer" style="position: relative;">
  <!-- iframe se sem načte -->
</div>

<div data-mobile-preview-target="mainContent">
  <%= yield %>
</div>

<style>
    .radio-label {
        display: flex;
        align-items: center;
        border: 1px solid #ccc;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.3s ease;
    }

    .radio-label:hover {
        background-color: #f0f0f0;
    }

    .radio-button {
        display: none;
    }

    .button-label {
        padding: 2px 8px;
    }

    .radio-button:checked + .button-label {
        background-color: #007bff;
        color: white;
        border-radius: 4px;
    }

</style>

</body>
</html>
