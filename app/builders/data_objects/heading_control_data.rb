# frozen_string_literal: true

class HeadingControlData
  attr_reader :id, :text, :pre_header, :position

  def initialize(id, text, pre_header, position: nil)
    @id = id
    @text = text
    @pre_header = pre_header
    @position = position
  end

  def type
    "BlockControls::Heading"
  end

  def options
    {}
  end

  def component
    BlockControls::HeadingControl.new(self)
  end

  def self.build_from_yaml(hash)
    new(SecureRandom.hex(3), hash[:text], hash[:options][:pre_header], position: hash[:position])
  end

  def self.build_from_record(block_control)
    new(block_control.id, block_control.text, block_control.pre_header, position: block_control.position)
  end
end
