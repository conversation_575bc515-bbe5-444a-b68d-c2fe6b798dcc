class MediaOptionsData
  attr_accessor :posts_limit, :position, :gap, :type, :layout

  def initialize(posts_limit:, position:, gap:, type:, layout:)
    @posts_limit = posts_limit
    @position = position
    @gap = gap
    @type = type
    @layout = layout
  end

  def to_hash
    {
      posts_limit: @posts_limit,
      position: @position,
      gap: @gap,
      type: @type,
      layout: @layout
    }
  end
end