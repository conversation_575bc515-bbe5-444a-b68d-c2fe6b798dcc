# frozen_string_literal: true

class ParagraphControlData
  attr_reader :id, :text, :options, :position

  def initialize(id, text, options, position: nil)
    @id = id
    @text = text
    @options = options
    @position = position
  end
  def type
    "BlockControls::Paragraph"
  end

  def component
    BlockControls::ParagraphControl.new(self)
  end

  def self.build_from_yaml(hash)
    new(SecureRandom.hex(3), hash[:text], hash[:options], position: hash[:position])
  end

  def self.build_from_record(block_control)
    new(block_control.id, block_control.text, block_control.options, position: block_control.position)
  end

end
