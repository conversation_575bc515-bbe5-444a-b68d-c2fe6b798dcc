# frozen_string_literal: true

class DividerControlData
  attr_reader :id, :width, :height, :position

  def initialize(id, options, position: nil)
    @id = id
    @width = options[:width]
    @height = options[:height]
    @position = position
  end

  def type
    "BlockControls::Divider"
  end

  def component
    BlockControls::DividerControl.new(self)
  end

  def self.build_from_yaml(hash)
    new(SecureRandom.hex(3), hash[:options], position: hash[:position])
  end

  def self.build_from_record(block_control)
    new(block_control.id, block_control.options.symbolize_keys, position: block_control.position)
  end
end
