# app/dtos/pricing_data.rb

PricingItemData = Struct.new(:name, :price, :price_eur, :is_price_textual?, :content_present?, :price_textual, :description, :content, :duration, :image_url, :slug, keyword_init: true)
PricingSectionData = Struct.new(:name, :items, keyword_init: true)

class PricingData
  attr_reader :name, :sections

  def initialize(name:, sections: [])
    @name = name
    @sections = sections
  end
end
