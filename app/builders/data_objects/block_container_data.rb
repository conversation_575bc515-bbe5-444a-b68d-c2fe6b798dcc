# frozen_string_literal: true

class BlockContainerData
  attr_reader :type, :theme, :container, :padding_x, :padding_y, :gap, :gap_y, :media_alignment, :alignment, :background_overlay_opacity

  def initialize(type, theme, container, padding_x, padding_y, gap, gap_y, media_alignment, alignment, background_overlay_opacity)
    @type = type
    @theme = theme
    @container = container
    @padding_x = padding_x
    @padding_y = padding_y
    @gap = gap
    @gap_y = gap_y
    @media_alignment = media_alignment
    @alignment = alignment
    @background_overlay_opacity = background_overlay_opacity
  end

  def self.build_from_block_container(block_container)
    new(
      block_container.type,
      block_container.theme,
      block_container.container,
      block_container.padding_x,
      block_container.padding_y,
      block_container.gap,
      block_container.gap_y,
      block_container.media_alignment,
      block_container.alignment,
      block_container.background_overlay_opacity
    )
  end

  def self.build_from_hash(hash)
    new(
      hash[:type],
      hash[:theme],
      hash[:container],
      hash[:padding_x],
      hash[:padding_y],
      hash[:gap],
      hash[:gap_y],
      hash[:media_alignment],
      hash[:alignment],
      hash[:background_overlay_opacity]
    )
  end
end
