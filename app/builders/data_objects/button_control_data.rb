# frozen_string_literal: true
PrimaryButtonData = Struct.new(:text, :link, :email, :phone, :link_type, :page_id, keyword_init: true)
SecondaryButtonData = Struct.new(:text, :link, :email, :phone, :link_type, :page_id, keyword_init: true)

class ButtonControlData
  attr_reader :id, :primary_button_data, :secondary_button_data, :position

  def initialize(id, primary_button_data, secondary_button_data, position: nil)
    @id = id
    @primary_button_data = primary_button_data
    @secondary_button_data = secondary_button_data
    @position = position
  end

  def component
    BlockControls::ButtonControl.new(self)
  end

  def type
    "BlockControls::Button"
  end

  def self.build_from_yaml(hash)
    primary_button_data = PrimaryButtonData.new(
      text: hash[:options][:primary_button_text],
      link: hash[:options][:primary_button_link],
      email: hash[:options][:primary_button_email],
      phone: hash[:options][:primary_button_phone],
      link_type: hash[:options][:primary_button_link_type],
      page_id: nil
    )

    secondary_button_data = SecondaryButtonData.new(
      text: hash[:options][:secondary_button_text],
      link: hash[:options][:secondary_button_link],
      email: hash[:options][:secondary_button_email],
      phone: hash[:options][:secondary_button_phone],
      link_type: hash[:options][:secondary_button_link_type],
      page_id: nil
    )

    new(SecureRandom.hex(3), primary_button_data, secondary_button_data, position: hash[:position])
  end

  def self.build_from_record(block_control)
    primary_button_data = PrimaryButtonData.new(
      text: block_control.primary_button_text,
      link: block_control.primary_button_link,
      email: block_control.primary_button_email,
      phone: block_control.primary_button_phone,
      link_type: block_control.primary_button_link_type,
      page_id: block_control.primary_button_page_id
    )

    secondary_button_data = SecondaryButtonData.new(
      text: block_control.secondary_button_text,
      link: block_control.secondary_button_link,
      email: block_control.secondary_button_email,
      phone: block_control.secondary_button_phone,
      link_type: block_control.secondary_button_link_type,
      page_id: block_control.secondary_button_page_id
    )

    new(block_control.id, primary_button_data, secondary_button_data, position: block_control.position)
  end
end
