class BlockFactory
  def self.build_block_data(source:, locale:)
    data = ConfigAdapter.for(source)
    return nil unless data

    BlockData.new(
      id: data.id,
      name: data.name,
      containers: data.containers,
      controls_data: data.controls_data,
      media_items: data.media_items,
      media_options: data.media_options,
      background_image: data.background_image_attachment,
      background_image_mobile: data.background_image_mobile_attachment,
      pricing_data: data.pricing_data
    )
  end

  def self.create_presenter(source:, view_context:)
    block_data = build_block_data(source: source, locale: view_context.locale)
    return nil unless block_data

    position = nil

    if view_context.current_page.present? && source.is_a?(Block)
      position = source.page_blocks.find_by(page: view_context.current_page)&.position
    end

    BlockPresenter.new(
      id: block_data.id,
      name: block_data.name,
      containers: block_data.containers,
      position: position,
      view_context: view_context,
      controls: block_data.controls_data,
      media_items: block_data.media_items,
      media_options: block_data.media_options,
      pricing_data: block_data.pricing_data,
      background_image: block_data.background_image,
      background_image_mobile: block_data.background_image_mobile,
    )
  end

  def self.build_component(source:, context:)
    presenter = create_presenter(source: source, view_context: context)
    return nil unless presenter

    component_class = presenter.component_class
    return nil unless component_class

    component_class.new(block_presenter: presenter, view_context: context)
  end

  def self.build_by_type(type, view_context:)
    Rails.application.config.x.components_by_type.fetch(type, {}).map do |key, _|
      self.create_presenter(source: key.to_s, view_context: view_context).component
    end
  end
end