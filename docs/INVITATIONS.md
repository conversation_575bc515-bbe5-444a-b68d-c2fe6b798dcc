# Systém správy uživatelů a pozvánek

Tento dokument popisuje implementaci systému sprá<PERSON> u<PERSON>, který umožňuje stávajícím správcům spravovat přístup k účtu a pozvat nové uživatele do správy daného Account.

## Přehled funkcionalit

### 1. <PERSON><PERSON><PERSON> model

**Model Invitation** obsahuje následující atributy:
- `email` - E-mailová adresa pozvaného uživatele
- `token` - Unikátní a bezpečný token pro identifikaci pozvánky (generován automaticky)
- `account_id` - Vazba na Account, ke kterému je uživatel zván
- `user_id` - Vazba na User, který pozvánku odeslal
- `accepted_at` - Datum a čas přijet<PERSON> p<PERSON> (NULL pro nevyřízené pozvánky)

**Validace:**
- E-mail musí být validní a unikátní v rámci účtu pro nevyřízené p<PERSON>
- Token je automaticky generován a musí být unikátní
- Zabránění duplicitním pozvánkám pro stejný e-mail v rámci účtu

**Scopes:**
- `pending` - Nevyřízené pozvánky
- `accepted` - Přijaté pozvánky
- `recent` - Seřazené podle data vytvoření (nejnovější první)
- `valid` - Pozvánky mladší než 7 dní

### 2. Routování

**Admin routes (v namespace admin/settings):**
- `GET /admin/:website_id/settings/users` - Seznam správců a pozvánek
- `POST /admin/:website_id/settings/users/create_invitation` - Vytvoření nové pozvánky
- `DELETE /admin/:website_id/settings/users/:id` - Odebrání správce

**Veřejné routes:**
- `GET /invitations/:token/accept` - Přijetí pozvánky
- `POST /invitations/:token/register` - Registrace nového uživatele

### 3. Controllery

**Admin::Settings::UsersController:**
- Správa uživatelů a pozvánek v administraci
- Zobrazení seznamu aktivních správců
- Vytváření pozvánek a odebrání správců
- Kontrola duplicitních pozvánek a existujících uživatelů
- Odesílání e-mailů s pozvánkami

**InvitationsController:**
- Zpracování přijetí pozvánek
- Rozlišení mezi existujícími a novými uživateli
- Kontrola expirace pozvánek

### 4. E-mailový systém

**InvitationMailer:**
- Odesílání pozvánek s unikátním odkazem
- HTML a textové šablony
- Informace o expiraci (7 dní)

### 5. Uživatelské rozhraní

**Admin interface:**
- Seznam aktivních správců s možností odebrání
- Seznam odeslaných pozvánek se stavem (čeká/přijato/vypršelo)
- Inline formulář pro odeslání nové pozvánky
- Integrace do admin settings navigace jako "Správci"

**Veřejné rozhraní:**
- Stránka pro přijetí pozvánky
- Registrační formulář pro nové uživatele
- Informace o expiraci pozvánky

## Workflow

### Scénář 1: Existující uživatel
1. Admin odešle pozvánku na e-mail existujícího uživatele
2. Uživatel klikne na odkaz v e-mailu
3. Systém automaticky vytvoří propojení s účtem
4. Pozvánka je označena jako přijatá
5. Uživatel je přesměrován na přihlášení

### Scénář 2: Nový uživatel
1. Admin odešle pozvánku na e-mail nového uživatele
2. Uživatel klikne na odkaz v e-mailu
3. Systém zobrazí registrační formulář s předvyplněným e-mailem
4. Po registraci je vytvořeno propojení s účtem
5. Pozvánka je označena jako přijatá
6. Uživatel je přesměrován na přihlášení

## Bezpečnostní opatření

- **Unikátní tokeny** - Každá pozvánka má bezpečný token generovaný Rails
- **Expirace** - Pozvánky automaticky vyprší po 7 dnech
- **Validace duplicit** - Zabránění více pozvánkám pro stejný e-mail
- **Kontrola oprávnění** - Pouze správci mohou odesílat pozvánky

## Údržba

**Rake tasky:**
- `rails invitations:cleanup` - Smazání vypršelých pozvánek
- `rails invitations:stats` - Zobrazení statistik pozvánek

**Doporučení:**
- Pravidelně spouštět cleanup task (např. denně přes cron)
- Monitorovat statistiky pozvánek pro analýzu využití

## Konfigurace

**Mailer:**
- Výchozí odesílatel: `<EMAIL>`
- URL host pro development: `localhost:3000`

**Expirace:**
- Výchozí doba expirace: 7 dní
- Lze upravit v modelu Invitation (metody `expired?` a scope `valid`)
