{"name": "app", "private": true, "scripts": {"build": "bun bun.config.js"}, "dependencies": {"@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@hotwired/stimulus": "^3.2.2", "@hotwired/turbo-rails": "^8.0.16", "@josefarias/hotwire_combobox": "^0.4.0", "@rails/actiontext": "^8.0.200", "@rails/activestorage": "^8.0.200", "@rails/request.js": "^0.0.12", "@splidejs/splide": "^4.1.4", "@stimulus-components/color-picker": "^2.0.0", "@stimulus-components/notification": "^3.0.0", "@stimulus-components/rails-nested-form": "^5.0.0", "@stimulus-components/reveal": "^5.0.0", "@stimulus-components/scroll-to": "^5.0.1", "@stimulus-components/sortable": "^5.0.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tiptap/core": "^2.23.0", "@tiptap/extension-bubble-menu": "^2.23.0", "@tiptap/extension-color": "^2.23.0", "@tiptap/extension-image": "^2.23.0", "@tiptap/extension-link": "^2.23.0", "@tiptap/extension-text-style": "^2.23.0", "@tiptap/extension-underline": "^2.23.0", "@tiptap/pm": "^2.23.0", "@tiptap/react": "^2.23.0", "@tiptap/starter-kit": "^2.23.0", "air-datepicker": "^3.6.0", "autoprefixer": "latest", "caniuse-lite": "^1.0.30001726", "daisyui": "^5.0.43", "nouislider": "^15.8.1", "prop-types": "^15.8.1", "react": "^19.1.0", "react-dom": "^19.1.0", "sortablejs": "^1.15.6", "stimulus-sortable": "^4.1.1", "stimulus-use": "^0.52.3", "tailwindcss-stimulus-components": "^6.1.3", "throttle-debounce": "^5.0.2", "tippy.js": "^6.3.7", "trix": "^2.1.15", "use-debounce": "^10.0.5"}}